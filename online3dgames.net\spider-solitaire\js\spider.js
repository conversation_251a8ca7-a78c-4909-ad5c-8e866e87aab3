class SpiderSolitaire {
    constructor(testMode = false) {
        this.testMode = testMode;
        this.deck = [];
        this.stock = [];
        this.tableau = [[], [], [], [], [], [], [], [], [], []]; // 10 piles for spider
        this.completed = []; // Completed sequences (K to A)
        this.score = 0;
        this.moves = 0;
        this.startTime = null;
        this.timer = null;
        this.gameWon = false;
        this.moveHistory = [];
        this.difficulty = 'easy'; // easy, medium, hard

        this.draggedCards = null;
        this.draggedFrom = null;
        this.draggedElement = null;
        this.draggedElements = null;
        this.isDragging = false;
        this.justFinishedDrag = false;
        this.dragOffset = { x: 0, y: 0 };
        this.dragStartPos = { x: 0, y: 0 };
        this.dragThreshold = 8;
        this.longPressTimer = null;
        this.longPressDelay = 300;
        this.hasAutoFullscreened = false;

        // Spider solitaire uses 2 decks (104 cards)
        this.suits = ['spades', 'hearts', 'clubs', 'diamonds'];
        this.ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        this.suitSymbols = { hearts: '♥', diamonds: '♦', clubs: '♣', spades: '♠' };
        this.suitColors = { hearts: 'red', diamonds: 'red', clubs: 'black', spades: 'black' };

        this.isAnimatingCard = false;
        this.isAutoCompleting = false;

        this.initializeGame();
        if (!testMode) {
            this.bindEvents();
        }
    }

    // Enhanced animation management methods
    async animateCardFlight($card, startPos, endPos, animationType = 'flying-to-tableau', duration = 400) {
        return new Promise((resolve) => {
            if (!$card.length) {
                resolve();
                return;
            }

            // Store original position and styles
            const originalPosition = $card.position();
            const originalZIndex = $card.css('z-index');
            const originalTransition = $card.css('transition');
            const originalTransform = $card.css('transform');

            // Calculate card dimensions for proper positioning
            const cardWidth = $card.outerWidth();
            const cardHeight = $card.outerHeight();

            // Set initial position (center of card)
            $card.css({
                position: 'fixed',
                left: (startPos.x - cardWidth / 2) + 'px',
                top: (startPos.y - cardHeight / 2) + 'px',
                zIndex: 10000,
                transition: 'none',
                transform: 'translateZ(0)'
            });

            // Add animation class
            $card.addClass(animationType);

            // Force reflow
            $card[0].offsetHeight;

            // Animate to end position (center of card) with smooth linear motion
            $card.css({
                left: (endPos.x - cardWidth / 2) + 'px',
                top: (endPos.y - cardHeight / 2) + 'px',
                transition: `all ${duration}ms linear`
            });

            // Clean up after animation
            setTimeout(() => {
                $card.removeClass(animationType);
                $card.css({
                    position: '',
                    left: '',
                    top: '',
                    zIndex: originalZIndex,
                    transition: originalTransition,
                    transform: originalTransform
                });
                resolve();
            }, duration);
        });
    }

    async animateDealFromStock() {
        const stockPosition = $('#stock').offset();
        if (!stockPosition) return;

        const cardsToDeal = [];
        const cardsToAdd = [];
        
        // Prepare cards to deal
        for (let i = 0; i < 10 && this.stock.length > 0; i++) {
            const card = this.stock.pop();
            card.faceUp = true;
            cardsToAdd.push({ card, targetPile: i });
            
            // Create temporary card element for animation
            const $tempCard = this.createCardElement(card);
            $tempCard.addClass('flying-from-stock');
            $tempCard.css({
                position: 'fixed',
                left: (stockPosition.left + 20) + 'px',
                top: (stockPosition.top + 20) + 'px',
                zIndex: 10001,
                transform: 'scale(1.05)'
            });
            $('body').append($tempCard);
            cardsToDeal.push({ $tempCard, targetPile: i });
        }

        // Animate each card to its target pile with improved positioning
        const animations = cardsToDeal.map(({ $tempCard, targetPile }, index) => {
            // Get target pile and its last card position
            const $pile = $(`#tableau-${targetPile}`);
            const $lastCard = $pile.find('.card').last();
            let targetRect;
            
            if ($lastCard.length) {
                targetRect = $lastCard[0].getBoundingClientRect();
            } else {
                targetRect = $pile[0].getBoundingClientRect();
            }
            
            // Calculate offset based on screen size
            const offset = window.innerWidth < 1024 ? 15 : 25;
            
            return new Promise((resolve) => {
                setTimeout(() => {
                    $tempCard.css({
                        left: targetRect.left + 'px',
                        top: (targetRect.top + offset) + 'px',
                        transition: 'all 0.45s linear',
                        transform: 'scale(1)'
                    });
                    
                    setTimeout(() => {
                        $tempCard.remove();
                        // Add card to tableau immediately after animation
                        cardsToAdd[index].card.targetPile = targetPile;
                        this.tableau[targetPile].push(cardsToAdd[index].card);
                        this.updateDisplay();
                        resolve();
                    }, 450);
                }, index * 60); // Keep original stagger timing
            });
        });

        await Promise.all(animations);
    }

    async animateCompletedSequence(cards, pileIndex) {
        const pileElement = $(`#tableau-${pileIndex}`);
        const pilePosition = pileElement.offset();
        
        // Get the actual card elements from the DOM to get their real positions
        const $pile = $(`#tableau-${pileIndex}`);
        const $actualCards = $pile.find('.card').slice(-13); // Get the last 13 cards
        
        // Create temporary elements for each card in the sequence, starting from the bottom card
        const cardElements = [];
        for (let i = 0; i < 13; i++) { // Start from bottom card (index 0) to top card (index 12)
            const card = cards[i];
            const $actualCard = $actualCards.eq(i); // Get corresponding actual card element
            
            const $tempCard = this.createCardElement(card);
            $tempCard.addClass('flying-to-completed');
            
            // Get the actual position of the card in the pile
            const actualCardRect = $actualCard[0].getBoundingClientRect();
            
            $tempCard.css({
                position: 'fixed',
                left: actualCardRect.left + 'px',
                top: actualCardRect.top + 'px',
                zIndex: 10000 + (12 - i), // Higher z-index for cards that should appear on top
                transform: 'scale(1)'
            });
            $('body').append($tempCard);
            cardElements.push({ $tempCard, card, index: i });
        }

        // Animate cards to completed area with improved positioning
        const completedArea = $('.completed-sequences');
        const completedPosition = completedArea.offset();
        const cardWidth = cardElements[0].$tempCard.outerWidth();
        const cardHeight = cardElements[0].$tempCard.outerHeight();
        
        // Animate cards in parallel with staggered timing
        const animations = cardElements.map(({ $tempCard, card, index }, i) => {
            return new Promise((resolve) => {
                setTimeout(() => {
                    $tempCard.css({
                        left: (completedPosition.left + completedArea.outerWidth() / 2 - cardWidth / 2 + index * 3) + 'px',
                        top: (completedPosition.top + completedArea.outerHeight() / 2 - cardHeight / 2) + 'px',
                        transition: 'all 0.4s linear',
                        transform: 'scale(0.9)'
                    });
                    
                    setTimeout(() => {
                        $tempCard.remove();
                        resolve();
                    }, 400);
                }, i * 60); // Stagger timing like deal animation
            });
        });

        await Promise.all(animations);
        
        // Remove cards from tableau after animation
        this.tableau[pileIndex] = this.tableau[pileIndex].slice(0, -13);
        this.completed.push(cards);
        
        // Update display to show completed sequences
        this.updateDisplay();
    }

    // Remove flip animation method since it's not needed
    // async animateCardFlip($card) {
    //     return new Promise((resolve) => {
    //         $card.addClass('flip-reveal');
    //         
    //         setTimeout(() => {
    //             $card.removeClass('flip-reveal');
    //             resolve();
    //         }, 500);
    //     });
    // }

    getCardPosition($card) {
        const offset = $card.offset();
        const width = $card.outerWidth();
        const height = $card.outerHeight();
        
        return {
            x: offset.left + width / 2,
            y: offset.top + height / 2
        };
    }

    getTableauPilePosition(pileIndex) {
        const $pile = $(`#tableau-${pileIndex}`);
        const offset = $pile.offset();
        const height = $pile.outerHeight();
        const width = $pile.outerWidth();
        
        // Get the last card in the pile to position the flying card correctly
        const $lastCard = $pile.find('.card').last();
        let targetY = offset.top + height - 20; // Default position
        
        if ($lastCard.length) {
            const lastCardOffset = $lastCard.offset();
            const lastCardHeight = $lastCard.outerHeight();
            targetY = lastCardOffset.top + lastCardHeight + 8; // Position below the last card with small gap
        }
        
        return {
            x: offset.left + width / 2,
            y: targetY
        };
    }

    getCompletedAreaPosition() {
        const $completedArea = $('.completed-sequences');
        const offset = $completedArea.offset();
        
        return {
            x: offset.left + $completedArea.outerWidth() / 2,
            y: offset.top + $completedArea.outerHeight() / 2
        };
    }

    getStockPosition() {
        const $stock = $('#stock');
        const offset = $stock.offset();
        
        return {
            x: offset.left + $stock.outerWidth() / 2,
            y: offset.top + $stock.outerHeight() / 2
        };
    }

    setDifficulty(difficulty) {
        this.difficulty = difficulty;
        
        // Update difficulty button states
        $('.btn-difficulty').removeClass('active');
        $(`#${difficulty}Btn`).addClass('active');
        
        this.newGame();
    }

    initializeGame() {
        this.deck = this.createDeck();
        this.dealCards();
        this.updateDisplay();
        this.startTimer();
    }

    createDeck() {
        const suits = this.getSuitsForDifficulty();
        const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        let idCounter = 1;
        const deck = [];
        for (let suit of suits) {
            for (let i = 0; i < 8; i++) { // 8副牌
                for (let rank of ranks) {
                    deck.push({
                        id: idCounter++,
                        suit,
                        rank,
                        value: this.getCardValue(rank),
                        color: 'black',
                        faceUp: false
                    });
                }
            }
        }
        return deck;
    }

    getSuitsForDifficulty() {
        switch (this.difficulty) {
            case 'easy':
                return ['spades']; // Only spades, but we'll create 8 copies to fill 104 cards
            case 'medium':
                return ['spades', 'hearts']; // 2 suits, 4 copies each
            case 'hard':
                return ['spades', 'hearts', 'clubs', 'diamonds']; // All 4 suits, 2 copies each
            default:
                return ['spades'];
        }
    }

    getCardValue(rank) {
        if (rank === 'A') return 1;
        if (rank === 'J') return 11;
        if (rank === 'Q') return 12;
        if (rank === 'K') return 13;
        return parseInt(rank);
    }

    dealCards() {
        this.stock = [];
        this.tableau = [[], [], [], [], [], [], [], [], [], []];
        this.completed = [];

        // Deal cards to tableau: first 4 piles get 6 cards, last 6 piles get 5 cards
        let cardIndex = 0;
        
        // First 4 piles: 6 cards each (5 face down, 1 face up)
        for (let pile = 0; pile < 4; pile++) {
            for (let card = 0; card < 6; card++) {
                if (cardIndex < this.deck.length) {
                    const currentCard = this.deck[cardIndex];
                    currentCard.faceUp = (card === 5); // Last card face up
                    this.tableau[pile].push(currentCard);
                    cardIndex++;
                }
            }
        }

        // Last 6 piles: 5 cards each (4 face down, 1 face up)
        for (let pile = 4; pile < 10; pile++) {
            for (let card = 0; card < 5; card++) {
                if (cardIndex < this.deck.length) {
                    const currentCard = this.deck[cardIndex];
                    currentCard.faceUp = (card === 4); // Last card face up
                    this.tableau[pile].push(currentCard);
                    cardIndex++;
                }
            }
        }

        // Remaining cards go to stock
        while (cardIndex < this.deck.length) {
            this.stock.push(this.deck[cardIndex]);
            cardIndex++;
        }
    }

    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }

    // Check if cards can be moved together (same suit, descending order)
    canMoveCardsTogether(cards) {
        if (cards.length <= 1) return true;
        
        for (let i = 0; i < cards.length - 1; i++) {
            const currentCard = cards[i];
            const nextCard = cards[i + 1];
            
            // Must be same suit and descending order
            if (currentCard.suit !== nextCard.suit || 
                currentCard.value !== nextCard.value + 1) {
                return false;
            }
        }
        return true;
    }

    // Get movable cards from a tableau pile starting from a specific index
    getMovableCards(pileIndex, startIndex) {
        const pile = this.tableau[pileIndex];
        if (startIndex >= pile.length) return [];
        
        // 只有最上面的牌可以被移动
        if (startIndex !== pile.length - 1) {
            return [];
        }
        
        // 从最上面的牌开始，向下查找连续的、同花色的递减序列
        const movableCards = [];
        const topCard = pile[startIndex];
        
        if (!topCard.faceUp) return [];
        
        movableCards.push(topCard);
        
        // 从最上面的牌开始，向下查找连续的、同花色的递减序列
        for (let i = startIndex - 1; i >= 0; i--) {
            const currentCard = pile[i];   // 下面的牌
            
            if (!currentCard.faceUp) break;
            
            // 检查当前牌是否与已收集的牌形成连续序列
            const lastCard = movableCards[movableCards.length - 1];
            
            // 检查是否有相同的牌
            if (lastCard.rank === currentCard.rank && lastCard.suit === currentCard.suit) {
                console.log(`发现相同牌：${currentCard.rank}${this.suitSymbols[currentCard.suit]}，不添加到可移动序列`);
                break; // 相同牌不添加到可移动序列
            }
            
            // 必须是同花色且递减顺序（下面的牌值比上面的牌值大1）
            // 例如：8 -> 9 (9 = 8 + 1), 7 -> 8 (8 = 7 + 1)
            if (lastCard.suit === currentCard.suit && 
                currentCard.value === lastCard.value + 1) {
                movableCards.push(currentCard);
            } else {
                break;
            }
        }
        
        return movableCards;
    }

    // Check if a card can be placed on another card
    canPlaceCard(cardToPlace, targetCard) {
        if (!targetCard) return true; // Can place on empty pile
        
        // In spider solitaire, cards can be placed in descending order regardless of suit
        // But moving together requires same suit
        const canPlace = cardToPlace.value === targetCard.value - 1;
        
        // 调试信息：打印详细的匹配逻辑
        console.log(`  canPlaceCard 详细检查:`);
        console.log(`    要放置的牌：${cardToPlace.rank}${this.suitSymbols[cardToPlace.suit]} (值: ${cardToPlace.value})`);
        console.log(`    目标牌：${targetCard.rank}${this.suitSymbols[targetCard.suit]} (值: ${targetCard.value})`);
        console.log(`    检查条件：${cardToPlace.value} === ${targetCard.value} - 1`);
        console.log(`    结果：${canPlace}`);
        
        return canPlace;
    }

    // Check for completed sequences (K to A of same suit)
    async checkForCompletedSequences() {
        let foundCompleted = false;
        
        for (let pileIndex = 0; pileIndex < this.tableau.length; pileIndex++) {
            const pile = this.tableau[pileIndex];
            if (pile.length < 13) continue;
            
            // Check if top 13 cards form a complete sequence K to A
            const topCards = pile.slice(-13);
            if (this.isCompleteSequence(topCards)) {
                this.isAnimatingCard = true; // 开始动画，禁止其他交互
                // Animate the completed sequence
                await this.animateCompletedSequence(topCards, pileIndex);
                this.isAnimatingCard = false; // 动画结束，恢复交互
                
                this.score += 100; // Bonus for completing sequence
                foundCompleted = true;
                
                // Flip the new top card if it exists and is face down
                const newTopCard = this.tableau[pileIndex][this.tableau[pileIndex].length - 1];
                if (newTopCard && !newTopCard.faceUp) {
                    newTopCard.faceUp = true;
                    this.score += 5;
                }
            }
        }
        
        return foundCompleted;
    }

    isCompleteSequence(cards) {
        if (cards.length !== 13) return false;
        
        const suit = cards[0].suit;
        
        // Check if all cards are same suit and in descending order K to A
        for (let i = 0; i < 13; i++) {
            const expectedValue = 13 - i; // K=13, Q=12, ..., A=1
            if (cards[i].suit !== suit || cards[i].value !== expectedValue || !cards[i].faceUp) {
                return false;
            }
        }
        
        return true;
    }

    // Deal new cards from stock (one to each non-empty pile)
    async dealFromStock() {
        if (this.stock.length === 0) return false;
        
        // Check if all piles have at least one card
        for (let pile of this.tableau) {
            if (pile.length === 0) {
                alert('All piles must have at least one card before dealing new cards!');
                return false;
            }
        }
        
        this.isAnimatingCard = true; // 开始动画，禁止其他交互
        // Animate the deal from stock
        await this.animateDealFromStock();
        this.isAnimatingCard = false; // 动画结束，恢复交互
        
        this.moves++;
        this.recordMove({
            type: 'deal',
            count: Math.min(10, this.stock.length)
        });
        
        return true;
    }

    checkWinCondition() {
        // Win when all 8 sequences are completed (8 suits × 13 cards = 104 cards)
        const expectedSequences = this.difficulty === 'easy' ? 8 : 
                                 this.difficulty === 'medium' ? 8 : 8;
        return this.completed.length >= expectedSequences;
    }

    recordMove(move) {
        this.moveHistory.push(move);
    }

    bindEvents() {
        $('#newGameBtn').on('click', () => {
            if (!this.hasAutoFullscreened) {
                this.hasAutoFullscreened = true;
                this.autoFullscreen();
            }
            this.newGame();
        });
        
        $('#undoBtn').on('click', () => this.undoMove());
        $('#hintBtn').on('click', () => this.showHint());
        $('#helpBtn').on('click', () => this.showHelp());
        $('#closeHelpBtn').on('click', () => this.hideHelp());
        $('#closeHelpBtnBottom').on('click', () => this.hideHelp());
        $('#fullscreenBtn').on('click', () => this.toggleFullscreen());

        // Add difficulty selection buttons
        $('#easyBtn').on('click', () => this.setDifficulty('easy'));
        $('#mediumBtn').on('click', () => this.setDifficulty('medium'));
        $('#hardBtn').on('click', () => this.setDifficulty('hard'));

        // Add message button handlers
        $('#playAgainBtn').on('click', () => {
            this.hideMessage();
            this.newGame();
        });
        $('#closeMessageBtn').on('click', () => this.hideMessage());

        // Mouse and touch events
        $(document).on('mousedown', async (e) => await this.onPointerDown(e.originalEvent || e));
        $(document).on('mousemove', (e) => this.onPointerMove(e.originalEvent || e));
        $(document).on('mouseup', async (e) => await this.onPointerUp(e.originalEvent || e));

        document.addEventListener('touchstart', async (e) => await this.onPointerDown(e), { passive: false });
        document.addEventListener('touchmove', (e) => this.onPointerMove(e), { passive: false });
        document.addEventListener('touchend', async (e) => await this.onPointerUp(e), { passive: false });
        document.addEventListener('touchcancel', async (e) => await this.onPointerUp(e), { passive: false });

        $(document).on('dragstart', (e) => e.preventDefault());
        $(document).on('selectstart', (e) => e.preventDefault());
        $(document).on('keydown', (e) => this.onKeyDown(e));
        $(document).on('contextmenu', (e) => e.preventDefault());

        // Fullscreen events
        $(document).on('fullscreenchange webkitfullscreenchange mozfullscreenchange MSFullscreenChange', () => {
            this.updateFullscreenButton();
        });

        this.updateFullscreenButton();
    }

    newGame() {
        this.stopTimer();
        this.score = 0;
        this.moves = 0;
        this.gameWon = false;
        this.moveHistory = [];
        this.resetDragState();

        $('.card').removeClass('dragging moving-to-foundation moving-to-tableau card-returning');
        $('.card').css({
            left: '',
            top: '',
            position: '',
            'z-index': '',
            'transition': ''
        });

        this.hideMessage();
        this.hideHelp();
        this.initializeGame();
    }

    resetDragState() {
        this.isDragging = false;
        this.draggedCards = null;
        this.draggedFrom = null;
        this.draggedElement = null;
        this.draggedElements = null;
        this.justFinishedDrag = false;
        this.dragOffset = { x: 0, y: 0 };
        this.dragStartPos = { x: 0, y: 0 };
        this.isAnimatingCard = false;

        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
    }

    startTimer() {
        this.startTime = Date.now();
        this.timer = setInterval(() => {
            const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
            const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
            const seconds = (elapsed % 60).toString().padStart(2, '0');
            $('#timer').text(`${minutes}:${seconds}`);
        }, 1000);
    }

    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }

    formatTime() {
        if (!this.startTime) return '00:00';
        
        const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    async updateDisplay() {
        if (!this.testMode) {
            this.updateStock();
            this.updateTableau();
            this.updateStats();
            this.updateCompleted();
        }
        
        // Check for completed sequences
        const foundCompleted = await this.checkForCompletedSequences();
        
        // If completed sequences were found, update display again but don't recurse
        if (foundCompleted && !this.testMode) {
            this.updateStock();
            this.updateTableau();
            this.updateStats();
            this.updateCompleted();
        }
        
        // Check win condition
        if (this.checkWinCondition()) {
            this.gameWon = true;
            this.showWinMessage();
        }
    }

    updateStock() {
        const stockElement = $('#stock');
        stockElement.empty();

        if (this.stock.length > 0) {
            // Show stock as multiple card backs
            const cardBack = $('<div class="card-back"></div>');
            stockElement.append(cardBack);

            const countIndicator = $('<div class="stock-count"></div>');
            countIndicator.text(this.stock.length);
            countIndicator.css({
                position: 'absolute',
                top: '5px',
                right: '5px',
                background: 'rgba(0,0,0,0.7)',
                color: 'white',
                borderRadius: '10px',
                padding: '2px 6px',
                fontSize: '12px',
                fontWeight: 'bold',
                zIndex: 10
            });
            stockElement.append(countIndicator);

            stockElement.removeClass('empty');
        } else {
            stockElement.html('<div class="stock-empty">Empty</div>');
            stockElement.addClass('empty');
        }
    }

    updateTableau() {
        for (let i = 0; i < 10; i++) {
            const tableauElement = $(`#tableau-${i}`);
            tableauElement.empty();
            
            const spacing = window.innerWidth < 1024 ? 15 : 25;
            this.tableau[i].forEach((card, index) => {
                const cardElement = this.createCardElement(card);
                cardElement.css({
                    position: 'absolute',
                    top: `${index * spacing}px`,
                    zIndex: index + 1
                });

                if (card.faceUp) {
                    // 获取从最上面的牌开始的可移动序列
                    const topCardIndex = this.tableau[i].length - 1;
                    const movableCards = this.getMovableCards(i, topCardIndex);
                    // 用id判断
                    const isMovable = movableCards.some(movableCard => movableCard.id === card.id);
                    if (isMovable) {
                        cardElement.addClass('draggable-card');
                    } else {
                        cardElement.addClass('disabled-card');
                        cardElement.css({
                            'filter': 'grayscale(100%)',
                            'opacity': '0.6',
                            'cursor': 'not-allowed'
                        });
                    }
                }

                if (card.faceUp && index === this.tableau[i].length - 1) {
                    cardElement.addClass('top-card');
                }

                tableauElement.append(cardElement);
            });
        }
    }

    updateCompleted() {
        // Update completed sequences display
        const completedElement = $('#completed');
        if (completedElement.length) {
            completedElement.text(`Completed: ${this.completed.length}/8`);
        }
    }

    createCardElement(card) {
        const cardElement = $('<div>').addClass('card');

        if (!card.faceUp) {
            cardElement.addClass('face-down');
            return cardElement;
        }

        cardElement.addClass(card.color);
        cardElement.attr('data-suit', card.suit);
        cardElement.attr('data-rank', card.rank);
        cardElement.attr('data-value', card.value);

        const symbol = this.suitSymbols[card.suit];

        cardElement.html(`
            <div class="card-top">
                <span class="rank">${card.rank}</span>
                <span class="suit">${symbol}</span>
            </div>
            <div class="card-center">${symbol}</div>
            <div class="card-bottom">
                <span class="rank">${card.rank}</span>
                <span class="suit">${symbol}</span>
            </div>
        `);

        return cardElement;
    }

    updateStats() {
        const $score = $('#score');
        const $moves = $('#moves');
        const $completed = $('#completed');
        
        // Animate score update
        if ($score.text() !== this.score.toString()) {
            $score.addClass('score-updating');
            $score.text(this.score);
            setTimeout(() => $score.removeClass('score-updating'), 500);
        }
        
        // Animate moves update
        if ($moves.text() !== this.moves.toString()) {
            $moves.addClass('moves-updating');
            $moves.text(this.moves);
            setTimeout(() => $moves.removeClass('moves-updating'), 300);
        }
        
        // Animate completed update
        if ($completed.text() !== `${this.completed.length}/8`) {
            $completed.addClass('completed-updating');
            $completed.text(`${this.completed.length}/8`);
            setTimeout(() => $completed.removeClass('completed-updating'), 600);
        }
        
        $('#timer').text(this.formatTime());
    }

    // Placeholder methods for compatibility
    async onPointerDown(e) {
        if (this.isAnimatingCard) return; // 动画期间禁止拖动
        if (e.type === 'mousedown' && (e.which !== 1 && e.button !== 0)) return;

        this.resetDragState();
        const coords = this.getEventCoordinates(e);
        this.dragStartPos = { x: coords.clientX, y: coords.clientY };

        const target = e.target || e.srcElement;

        // Handle stock click
        if (target.closest('#stock') || $(target).closest('#stock').length) {
            e.preventDefault();
            e.stopPropagation();
            await this.dealFromStock();
            await this.checkForCompletedSequences();
            this.checkWinCondition();
            return;
        }

        const cardElement = target.closest('.card') || $(target).closest('.card')[0];
        if (!cardElement) return;

        e.preventDefault();
        e.stopPropagation();

        const $cardElement = $(cardElement);

        if ($cardElement.hasClass('face-down')) {
            return;
        }

        // 检查是否是禁用的牌
        if ($cardElement.hasClass('disabled-card')) {
            return; // 禁用的牌不能拖动
        }

        const cardRect = cardElement.getBoundingClientRect();
        this.dragOffset.x = coords.clientX - cardRect.left;
        this.dragOffset.y = coords.clientY - cardRect.top;

        this.prepareDragData($cardElement);
        
        // 如果没有可移动的牌，则不允许拖动
        if (!this.draggedCards || this.draggedCards.length === 0) {
            this.resetDragState();
            return;
        }

        const isMobile = e.type.startsWith('touch');
        if (isMobile) {
            this.longPressTimer = setTimeout(() => {
                if (!this.isDragging && this.draggedCards && this.draggedCards.length > 0) {
                    navigator.vibrate && navigator.vibrate(50);
                }
            }, this.longPressDelay);
        }
    }

    onPointerMove(e) {
        if (this.isAnimatingCard) return; // 动画期间禁止拖动
        if (!this.draggedCards || this.draggedCards.length === 0) return;

        const coords = this.getEventCoordinates(e);
        const deltaX = coords.clientX - this.dragStartPos.x;
        const deltaY = coords.clientY - this.dragStartPos.y;

        if (!this.isDragging) {
            if (Math.abs(deltaX) > this.dragThreshold || Math.abs(deltaY) > this.dragThreshold) {
                this.startDrag();
            }
            return;
        }

        e.preventDefault();
        e.stopPropagation();

        this.updateDragPosition(coords.clientX, coords.clientY);
        this.updateDropZones(coords.clientX, coords.clientY);
    }

    async onPointerUp(e) {
        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }

        if (!this.isDragging && this.draggedCards && this.draggedCards.length > 0) {
            // Handle click/tap without drag
            await this.handleCardClick();
            this.resetDragState();
            return;
        }

        if (this.isDragging) {
            await this.handleDrop(e);
        }

        this.resetDragState();
    }

    getEventCoordinates(e) {
        if (e.type && e.type.startsWith('touch')) {
            const touchEvent = e.originalEvent || e;
            if (touchEvent.touches && touchEvent.touches.length > 0) {
                const touch = touchEvent.touches[0];
                return {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                };
            } else if (touchEvent.changedTouches && touchEvent.changedTouches.length > 0) {
                const touch = touchEvent.changedTouches[0];
                return {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                };
            }
        }
        return {
            clientX: e.clientX || 0,
            clientY: e.clientY || 0
        };
    }

    prepareDragData($cardElement) {
        const pileElement = $cardElement.closest('.tableau-pile');
        if (!pileElement.length) return;

        const pileId = pileElement.attr('id');
        const pileIndex = parseInt(pileId.split('-')[1]);
        
        // 找到这张牌在牌堆中的实际索引
        const cards = pileElement.find('.card');
        let cardIndex = -1;
        for (let i = 0; i < cards.length; i++) {
            if (cards[i] === $cardElement[0]) {
                cardIndex = i;
                break;
            }
        }
        
        if (cardIndex === -1) return;

        this.draggedFrom = { type: 'tableau', index: pileIndex };
        
        // 获取从最上面的牌开始的可移动序列
        const topCardIndex = this.tableau[pileIndex].length - 1;
        this.draggedCards = this.getMovableCards(pileIndex, topCardIndex);
        
        // 调试信息：打印可移动的牌
        console.log(`=== 拖动调试信息 ===`);
        console.log(`点击的牌：${this.tableau[pileIndex][cardIndex].rank}${this.suitSymbols[this.tableau[pileIndex][cardIndex].suit]}`);
        console.log(`可移动的牌：${this.draggedCards.map(card => `${card.rank}${this.suitSymbols[card.suit]}`).join(', ')}`);
        
        // 检查点击的牌是否在可移动序列中（用id）
        const clickedCard = this.tableau[pileIndex][cardIndex];
        const isClickable = this.draggedCards.some(movableCard => movableCard.id === clickedCard.id);
        if (!isClickable) {
            console.log(`点击的牌不在可移动序列中，不允许拖动`);
            this.draggedCards = [];
            return;
        }
        if (this.draggedCards.length === 0) return;
        
        console.log(`开始拖动 ${this.draggedCards.length} 张牌`);
        
        // Store original positions - 只存储实际可移动的牌
        this.originalCardPositions = [];
        const cardsToMove = pileElement.find('.card').slice(cardIndex, cardIndex + this.draggedCards.length);
        cardsToMove.each((i, card) => {
            const $card = $(card);
            const rect = card.getBoundingClientRect();
            this.originalCardPositions.push({
                element: $card,
                left: rect.left,
                top: rect.top,
                position: $card.css('position'),
                zIndex: $card.css('z-index')
            });
        });
    }

    startDrag() {
        this.isDragging = true;
        // this.isAnimatingCard = true; // 不再在拖动时设置

        // 拖动时保持原样：保持原有的 z-index 和间距
        this.draggedElements = [];
        const spacing = window.innerWidth < 1024 ? 15 : 25;
        
        this.originalCardPositions.forEach((pos, index) => {
            const $card = pos.element;
            const rect = $card[0].getBoundingClientRect(); // 获取准确的屏幕坐标
            
            $card.css({
                position: 'fixed',
                left: rect.left + 'px',
                top: rect.top + 'px',
                zIndex: index + 9999, // 保持原有的 z-index
                pointerEvents: 'none',
                transition: 'none',
                transform: 'none' // 确保没有变换
            });
            this.draggedElements.push($card);
        });
    }

    updateDragPosition(clientX, clientY) {
        if (!this.draggedElements || this.draggedElements.length === 0) return;

        requestAnimationFrame(() => {
            // 再次检查，防止在 requestAnimationFrame 回调执行时 draggedElements 已被清空
            if (!Array.isArray(this.draggedElements) || this.draggedElements.length === 0) return;
            
            // 跟手：left/top基于clientX/clientY减去dragOffset
            const baseX = clientX - this.dragOffset.x;
            const baseY = clientY - this.dragOffset.y;
            const spacing = window.innerWidth < 1024 ? 15 : 25;

            this.draggedElements.forEach((element, index) => {
                element.css({
                    left: baseX + 'px',
                    top: (baseY + index * spacing) + 'px' // 保持原有的间距
                });
            });
        });
    }

    updateDropZones(clientX, clientY) {
        $('.drop-zone-valid, .drop-zone-invalid').removeClass('drop-zone-valid drop-zone-invalid');

        const elementBelow = document.elementFromPoint(clientX, clientY);
        if (!elementBelow) return;

        const $pile = $(elementBelow).closest('.tableau-pile');
        if (!$pile.length) return;

        // 下面的样式提示全部去掉，不再添加 drop-zone-valid/drop-zone-invalid
        // const pileIndex = parseInt($pile.attr('id').split('-')[1]);
        // const topCard = this.tableau[pileIndex].length > 0 ? 
        //                this.tableau[pileIndex][this.tableau[pileIndex].length - 1] : null;
        // if (this.canPlaceCard(this.draggedCards[0], topCard)) {
        //     $pile.addClass('drop-zone-valid');
        // } else {
        //     $pile.addClass('drop-zone-invalid');
        // }
    }

    async handleDrop(e) {
        const coords = this.getEventCoordinates(e);
        const elementBelow = document.elementFromPoint(coords.clientX, coords.clientY);
        
        console.log(`=== 放置调试信息 ===`);
        console.log(`放置位置元素：`, elementBelow);
        
        if (!elementBelow) {
            console.log(`没有找到目标元素，返回原位置`);
            this.returnCardsToOriginalPosition();
            return;
        }

        const $targetPile = $(elementBelow).closest('.tableau-pile');
        if (!$targetPile.length) {
            console.log(`没有找到目标牌堆，返回原位置`);
            this.returnCardsToOriginalPosition();
            return;
        }

        const targetPileIndex = parseInt($targetPile.attr('id').split('-')[1]);
        console.log(`目标牌堆索引：${targetPileIndex}`);
        
        if (this.isValidMove(targetPileIndex)) {
            console.log(`移动有效，执行移动`);
            await this.executeMove(targetPileIndex);
            await this.checkForCompletedSequences();
            this.checkWinCondition();
        } else {
            console.log(`移动无效，返回原位置`);
            this.returnCardsToOriginalPosition();
        }
    }

    async handleCardClick() {
        for (let i = 0; i < 10; i++) {
            if (i !== this.draggedFrom.index && this.isValidMove(i)) {
                // 找到所有要移动的牌和DOM元素
                const pileIndex = this.draggedFrom.index;
                const cards = this.draggedCards;
                const fromElements = [];
                for (let j = 0; j < cards.length; j++) {
                    const card = cards[j];
                    // 只取堆中从上往下第一个匹配的元素，保证顺序
                    const $el = $(`#tableau-${pileIndex} .card[data-suit='${card.suit}'][data-rank='${card.rank}']:eq(0)`);
                    fromElements.push($el);
                }
                await this.animateAutoMatchFlight(cards, fromElements, i); // 先动画
                await this.executeMove(i); // 再数据变更
                await this.checkForCompletedSequences();
                this.checkWinCondition();
                return;
            }
        }
    }

    // 自动匹配时的平移动画：多张牌一起飞（hearts风格，彻底无闪烁）
    async animateAutoMatchFlight(cards, fromElements, toPileIndex) {
        this.isAnimatingCard = true;
        // 目标堆
        const $pile = $(`#tableau-${toPileIndex}`);
        const $lastCard = $pile.find('.card').last();
        let targetRect;
        if ($lastCard.length) {
            targetRect = $lastCard[0].getBoundingClientRect();
        } else {
            targetRect = $pile[0].getBoundingClientRect();
        }
        // 间距
        const spacing = window.innerWidth < 1024 ? 15 : 25;

        // 1. 先隐藏原牌
        fromElements.forEach($el => $el.css('visibility', 'hidden'));

        // 2. 创建临时卡牌
        const tempCards = [];
        const startRects = fromElements.map($el => $el[0].getBoundingClientRect());
        for (let i = 0; i < cards.length; i++) {
            const $tempCard = this.createCardElement(cards[i]);
            $tempCard.addClass('flying-to-tableau-real');
            $tempCard.css({
                position: 'fixed',
                left: startRects[i].left + 'px',
                top: startRects[i].top + 'px',
                width: startRects[i].width + 'px',
                height: startRects[i].height + 'px',
                zIndex: 10001 + i, // 最下面的牌z-index最高
                transition: 'all 0.45s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                transformOrigin: 'center center',
                pointerEvents: 'none',
                margin: 0,
                boxSizing: 'border-box',
                borderRadius: '8px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.3)'
            });
            tempCards.push($tempCard);
        }

        // 3. append所有临时卡牌
        tempCards.forEach($el => $('body').append($el));

        // 4. 强制渲染一帧，确保隐藏生效
        await new Promise(resolve => requestAnimationFrame(resolve));

        // 5. 动画
        await new Promise(resolve => {
            requestAnimationFrame(() => {
                for (let i = 0; i < tempCards.length; i++) {
                    tempCards[i].css({
                        left: targetRect.left + 'px',
                        top: (targetRect.top + targetRect.height * 0.15 + i * spacing) + 'px',
                        width: startRects[i].width * 0.98 + 'px',
                        height: startRects[i].height * 0.98 + 'px'
                    });
                }
            });
            tempCards[0].on('transitionend', resolve);
            setTimeout(resolve, 500);
        });

        // 6. 移除临时卡牌
        tempCards.forEach($el => $el.remove());
        this.isAnimatingCard = false;
    }

    isValidMove(targetPileIndex) {
        if (targetPileIndex === this.draggedFrom.index) return false;
        
        const targetPile = this.tableau[targetPileIndex];
        const topCard = targetPile.length > 0 ? targetPile[targetPile.length - 1] : null;
        
        // 调试信息：打印匹配信息
        console.log(`=== 匹配调试信息 ===`);
        console.log(`拖动的第一张牌：${this.draggedCards[0].rank}${this.suitSymbols[this.draggedCards[0].suit]}`);
        if (topCard) {
            console.log(`目标牌：${topCard.rank}${this.suitSymbols[topCard.suit]}`);
        } else {
            console.log(`目标牌：空牌堆`);
        }
        
        const canPlace = this.canPlaceCard(this.draggedCards[0], topCard);
        console.log(`是否可以放置：${canPlace}`);
        
        return canPlace;
    }

    async executeMove(targetPileIndex) {
        this.isAnimatingCard = true; // 开始动画，禁止其他交互
        
        // 打印移动信息
        const sourcePileIndex = this.draggedFrom.index;
        const movedCards = this.draggedCards;
        const firstCard = movedCards[0];
        const lastCard = movedCards[movedCards.length - 1];
        
        console.log(`移动了第${sourcePileIndex + 1}组的${firstCard.rank}${this.suitSymbols[firstCard.suit]}`);
        if (movedCards.length > 1) {
            console.log(`到${lastCard.rank}${this.suitSymbols[lastCard.suit]}（共${movedCards.length}张牌）`);
        }
        console.log(`移动到了第${targetPileIndex + 1}组`);
        
        // 直接更新数据，不进行飞行动画
        // Remove cards from source pile
        const sourcePile = this.tableau[sourcePileIndex];
        const cardsToMove = sourcePile.splice(-movedCards.length);
        
        // Add cards to target pile
        this.tableau[targetPileIndex].push(...cardsToMove);
        
        // 打印移动后目标组的牌
        const targetPile = this.tableau[targetPileIndex];
        console.log(`移动后第${targetPileIndex + 1}组的牌：`);
        targetPile.forEach((card, index) => {
            if (card.faceUp) {
                console.log(`  ${index + 1}. ${card.rank}${this.suitSymbols[card.suit]}`);
            } else {
                console.log(`  ${index + 1}. [背面]`);
            }
        });
        
        // 检查并打印被禁用的牌
        const disabledCards = [];
        const faceUpCards = targetPile.filter(card => card.faceUp);
        
        console.log(`检查第${targetPileIndex + 1}组的牌：${faceUpCards.map(card => `${card.rank}${this.suitSymbols[card.suit]}`).join(', ')}`);
        
        // 获取从最上面的牌开始的可移动序列
        const topCardIndex = targetPile.length - 1;
        const movableCards = this.getMovableCards(targetPileIndex, topCardIndex);
        
        console.log(`可移动的牌：${movableCards.map(c => `${c.rank}${this.suitSymbols[c.suit]}`).join(', ')}`);
        
        // 检查每张牌是否在可移动序列中
        for (let i = 0; i < faceUpCards.length; i++) {
            const card = faceUpCards[i];
            const isMovable = movableCards.some(movableCard => 
                movableCard.suit === card.suit && 
                movableCard.rank === card.rank
            );
            
            if (!isMovable) {
                disabledCards.push(`${card.rank}${this.suitSymbols[card.suit]}`);
            }
        }
        
        if (disabledCards.length > 0) {
            console.log(`第${targetPileIndex + 1}组被禁用的牌：${disabledCards.join(', ')}`);
        } else {
            console.log(`第${targetPileIndex + 1}组没有被禁用的牌`);
        }
        
        // Flip new top card in source pile if needed
        if (sourcePile.length > 0) {
            const newTopCard = sourcePile[sourcePile.length - 1];
            if (!newTopCard.faceUp) {
                newTopCard.faceUp = true;
                this.score += 5;
            }
        }
        
        this.moves++;
        this.score += 1;
        
        // Add score update animation
        this.animateScoreUpdate();
        
        this.recordMove({
            type: 'move',
            from: this.draggedFrom,
            to: { type: 'tableau', index: targetPileIndex },
            cards: [...this.draggedCards]
        });
        
        this.updateDisplay();
        this.isAnimatingCard = false; // 动画结束，恢复交互
    }

    // Add score update animation
    animateScoreUpdate() {
        const $scoreElement = $('#score');
        $scoreElement.addClass('score-updating');
        setTimeout(() => {
            $scoreElement.removeClass('score-updating');
        }, 500);
    }

    returnCardsToOriginalPosition() {
        if (!Array.isArray(this.draggedElements) || this.draggedElements.length === 0) return;

        this.draggedElements.forEach((element, index) => {
            const originalPos = this.originalCardPositions[index];
            element.addClass('returning');
            element.css({
                left: originalPos.left + 'px',
                top: originalPos.top + 'px',
                transition: 'all 0.3s linear'
            });
        });

        setTimeout(() => {
            if (Array.isArray(this.draggedElements)) {
                this.draggedElements.forEach(element => {
                    element.removeClass('returning');
                });
            }
            this.updateDisplay();
        }, 300);
    }

    onKeyDown(e) {
        switch(e.key) {
            case 'Escape':
                this.hideHelp();
                this.hideMessage();
                break;
            case 'h':
            case 'H':
                if (!e.ctrlKey && !e.metaKey) {
                    this.showHint();
                }
                break;
            case 'n':
            case 'N':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    this.newGame();
                }
                break;
            case 'z':
            case 'Z':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    this.undoMove();
                }
                break;
            case ' ':
                e.preventDefault();
                this.dealFromStock();
                break;
        }
    }

    undoMove() {
        if (this.moveHistory.length === 0) return;
        
        const lastMove = this.moveHistory.pop();
        
        switch(lastMove.type) {
            case 'move':
                // Reverse the move
                const cardsToReverse = this.tableau[lastMove.to.index].splice(-lastMove.cards.length);
                this.tableau[lastMove.from.index].push(...cardsToReverse);
                
                // Handle face-down card that might have been flipped
                if (this.tableau[lastMove.from.index].length > lastMove.cards.length) {
                    const cardBelowMoved = this.tableau[lastMove.from.index][this.tableau[lastMove.from.index].length - lastMove.cards.length - 1];
                    if (cardBelowMoved && cardBelowMoved.faceUp && lastMove.flippedCard) {
                        cardBelowMoved.faceUp = false;
                        this.score -= 5;
                    }
                }
                
                this.moves--;
                this.score -= 1;
                break;
                
            case 'deal':
                // Reverse dealing cards
                for (let i = 0; i < 10 && this.tableau[i].length > 0; i++) {
                    const card = this.tableau[i].pop();
                    card.faceUp = false;
                    this.stock.push(card);
                }
                this.moves--;
                break;
        }
        
        this.updateDisplay();
    }

    showHint() {
        // Find possible moves and highlight them
        $('.hint-highlight').removeClass('hint-highlight');
        
        for (let fromPile = 0; fromPile < 10; fromPile++) {
            const pile = this.tableau[fromPile];
            if (pile.length === 0) continue;
            
            for (let cardIndex = 0; cardIndex < pile.length; cardIndex++) {
                const card = pile[cardIndex];
                if (!card.faceUp) continue;
                
                const movableCards = this.getMovableCards(fromPile, cardIndex);
                if (movableCards.length === 0) continue;
                
                for (let toPile = 0; toPile < 10; toPile++) {
                    if (toPile === fromPile) continue;
                    
                    const targetPile = this.tableau[toPile];
                    const topCard = targetPile.length > 0 ? targetPile[targetPile.length - 1] : null;
                    
                    if (this.canPlaceCard(movableCards[0], topCard)) {
                        // Highlight the movable card
                        const $pile = $(`#tableau-${fromPile}`);
                        const $card = $pile.find('.card').eq(cardIndex);
                        $card.addClass('hint-highlight');
                        
                        setTimeout(() => {
                            $('.hint-highlight').removeClass('hint-highlight');
                        }, 2000);
                        
                        return; // Show only first hint
                    }
                }
            }
        }
    }

    showHelp() {
        $('#helpPanel').removeClass('hidden');
    }

    hideHelp() {
        $('#helpPanel').addClass('hidden');
    }

    hideMessage() {
        $('#gameMessage').addClass('hidden');
    }

    showWinMessage() {
        $('#messageTitle').text('Congratulations!');
        $('#messageText').text('You completed Spider Solitaire!');
        $('#finalScore').text(this.score);
        $('#finalMoves').text(this.moves);
        
        const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
        const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
        const seconds = (elapsed % 60).toString().padStart(2, '0');
        $('#finalTime').text(`${minutes}:${seconds}`);
        
        $('#gameMessage').removeClass('hidden');
    }

    autoFullscreen() {
        if (document.documentElement.requestFullscreen) {
            document.documentElement.requestFullscreen().catch(() => {});
        } else if (document.documentElement.webkitRequestFullscreen) {
            document.documentElement.webkitRequestFullscreen();
        } else if (document.documentElement.mozRequestFullScreen) {
            document.documentElement.mozRequestFullScreen();
        } else if (document.documentElement.msRequestFullscreen) {
            document.documentElement.msRequestFullscreen();
        }
    }

    toggleFullscreen() {
        if (!document.fullscreenElement && !document.webkitFullscreenElement && 
            !document.mozFullScreenElement && !document.msFullscreenElement) {
            this.autoFullscreen();
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            }
        }
    }

    updateFullscreenButton() {
        const isFullscreen = !!(document.fullscreenElement || document.webkitFullscreenElement || 
                              document.mozFullScreenElement || document.msFullscreenElement);
        $('#fullscreenBtn').text(isFullscreen ? '⛶' : '⛶');
    }
}

// Initialize the game when DOM is loaded
$(document).ready(function() {
    window.game = new SpiderSolitaire();
});

