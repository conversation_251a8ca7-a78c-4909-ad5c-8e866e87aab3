class SolitaireGame {
    constructor(testMode = false) {
        this.testMode = testMode;
        this.deck = [];
        this.stock = [];
        this.waste = [];
        this.foundations = { hearts: [], diamonds: [], clubs: [], spades: [] };
        this.tableau = [[], [], [], [], [], [], []];
        this.score = 0;
        this.moves = 0;
        this.startTime = null;
        this.timer = null;
        this.gameWon = false;
        this.autoCompleteEnabled = true;
        this.moveHistory = [];

        this.draggedCards = null;
        this.draggedFrom = null;
        this.draggedElement = null;
        this.draggedElements = null;
        this.flipCardElement = null;
        this.isDragging = false;
        this.justFinishedDrag = false;
        this.dragOffset = { x: 0, y: 0 };
        this.dragStartPos = { x: 0, y: 0 };
        this.dragThreshold = 8;
        this.longPressTimer = null;
        this.longPressDelay = 300;
        this.hasAutoFullscreened = false;

        this.suits = ['hearts', 'diamonds', 'clubs', 'spades'];
        this.ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        this.suitSymbols = { hearts: '♥', diamonds: '♦', clubs: '♣', spades: '♠' };
        this.suitColors = { hearts: 'red', diamonds: 'red', clubs: 'black', spades: 'black' };

        this.initializeGame();
        if (!testMode) {
            this.bindEvents();
        }
        this.isAnimatingCard = false;
        this.isAutoCompleting = false;
    }

    initializeGame() {
        this.createDeck();
        this.dealSolvableGame();
        this.updateDisplay();
        this.startTimer();
    }

    createDeck() {
        this.deck = [];
        for (let suit of this.suits) {
            for (let rank of this.ranks) {
                this.deck.push({
                    suit: suit,
                    rank: rank,
                    color: this.suitColors[suit],
                    value: this.getCardValue(rank),
                    faceUp: false
                });
            }
        }
    }

    getCardValue(rank) {
        if (rank === 'A') return 1;
        if (rank === 'J') return 11;
        if (rank === 'Q') return 12;
        if (rank === 'K') return 13;
        return parseInt(rank);
    }

    dealCards() {
        this.stock = [...this.deck];
        this.waste = [];
        this.foundations = { hearts: [], diamonds: [], clubs: [], spades: [] };
        this.tableau = [[], [], [], [], [], [], []];
        for (let i = 0; i < 7; i++) {
            for (let j = i; j < 7; j++) {
                const card = this.stock.pop();
                if (i === j) {
                    card.faceUp = true;
                }
                this.tableau[j].push(card);
            }
        }
    }

    dealSolvableGame() {
        this.stock = [];
        this.waste = [];
        this.foundations = { hearts: [], diamonds: [], clubs: [], spades: [] };
        this.tableau = [[], [], [], [], [], [], []];

        this.createDeck();
        this.shuffleArray(this.deck);
        this.dealCardsStandard();
    }


    dealCardsStandard() {
        let cardIndex = 0;

        for (let row = 0; row < 7; row++) {
            for (let col = row; col < 7; col++) {
                if (cardIndex < this.deck.length) {
                    const card = this.deck[cardIndex];
                    card.faceUp = (row === col);
                    this.tableau[col].push(card);
                    cardIndex++;
                }
            }
        }

        while (cardIndex < this.deck.length) {
            const card = this.deck[cardIndex];
            card.faceUp = false;
            this.stock.push(card);
            cardIndex++;
        }
    }

    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }

    bindEvents() {
        $('#newGameBtn').on('click', () => {
            if (!this.hasAutoFullscreened) {
                this.hasAutoFullscreened = true;
                this.autoFullscreen();
            }
            this.newGame();
        });
        $('#undoBtn').on('click', () => this.undoMove());
        $('#hintBtn').on('click', () => this.showKlondikeHint());
        $('#helpBtn').on('click', () => this.showHelp());
        $('#closeHelpBtn').on('click', () => this.hideHelp());
        $('#closeHelpBtnBottom').on('click', () => this.hideHelp());
        $('#fullscreenBtn').on('click', () => this.toggleFullscreen());

        $(document).on('mousedown', (e) => this.onPointerDown(e.originalEvent || e));
        $(document).on('mousemove', (e) => this.onPointerMove(e.originalEvent || e));
        $(document).on('mouseup', (e) => this.onPointerUp(e.originalEvent || e));

        document.addEventListener('touchstart', (e) => this.onPointerDown(e), { passive: false });
        document.addEventListener('touchmove', (e) => this.onPointerMove(e), { passive: false });
        document.addEventListener('touchend', (e) => this.onPointerUp(e), { passive: false });
        document.addEventListener('touchcancel', (e) => this.onPointerUp(e), { passive: false });

        $(document).on('dragstart', (e) => e.preventDefault());
        $(document).on('selectstart', (e) => e.preventDefault());
        $(document).on('keydown', (e) => this.onKeyDown(e));
        $(document).on('contextmenu', (e) => e.preventDefault());

        // Listen for fullscreen change events
        $(document).on('fullscreenchange webkitfullscreenchange mozfullscreenchange MSFullscreenChange', () => {
            this.updateFullscreenButton();
        });

        // Initialize fullscreen button state
        this.updateFullscreenButton();
    }

    getEventCoordinates(e) {
        if (e.type && e.type.startsWith('touch')) {
            const touchEvent = e.originalEvent || e;
            if (touchEvent.touches && touchEvent.touches.length > 0) {
                const touch = touchEvent.touches[0];
                return {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                };
            } else if (touchEvent.changedTouches && touchEvent.changedTouches.length > 0) {
                const touch = touchEvent.changedTouches[0];
                return {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                };
            }
        }
        return {
            clientX: e.clientX || 0,
            clientY: e.clientY || 0
        };
    }

    onCardDoubleClick(cardElement) {
        if (!this.isTopCard(cardElement) || cardElement.hasClass('face-down')) {
            return;
        }
        this.smartAutoMove(cardElement);
    }

    isTopCard(cardElement) {
        if (cardElement.closest('.waste-pile').length) {
            return cardElement.hasClass('top-card');
        }
        if (cardElement.closest('.tableau-pile').length) {
            const pile = cardElement.closest('.tableau-pile');
            const cards = pile.find('.card');
            return cardElement.is(cards.last());
        }
        if (cardElement.closest('.foundation-pile').length) {
            const pile = cardElement.closest('.foundation-pile');
            const cards = pile.find('.card');
            return cards.length === 0 || cardElement.is(cards.last());
        }
        return false;
    }

    newGame() {
        this.stopTimer();
        this.score = 0;
        this.moves = 0;
        this.gameWon = false;
        this.moveHistory = [];
        this.resetDragState();

        $('.card').removeClass('dragging moving-to-foundation moving-to-tableau card-returning');
        $('.card').css({
            left: '',
            top: '',
            position: '',
            'z-index': '',
            'transition': ''
        });

        this.hideMessage();
        this.hideHelp();
        this.initializeGame();
    }

    resetDragState() {
        this.isDragging = false;
        this.draggedCards = null;
        this.draggedFrom = null;
        this.draggedElement = null;
        this.draggedElements = null;
        this.flipCardElement = null;
        this.justFinishedDrag = false;
        this.dragOffset = { x: 0, y: 0 };
        this.dragStartPos = { x: 0, y: 0 };
        this.originalCardPositions = [];
        this.isAnimatingCard = false;

        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
    }

    startTimer() {
        this.startTime = Date.now();
        this.timer = setInterval(() => {
            const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
            const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
            const seconds = (elapsed % 60).toString().padStart(2, '0');
            $('#timer').text(`${minutes}:${seconds}`);
        }, 1000);
    }

    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }

    async updateDisplay() {
        if (!this.testMode) {
            this.updateStock();
            this.updateWaste();
            this.updateFoundations();
            this.updateTableau();
            this.updateStats();
        }
        if (!this.isAnimatingCard && !this.isAutoCompleting) {
            await this.checkAutoCompleteTrigger();
        }
    }

    updateDisplayWithoutAutoComplete() {
        if (!this.testMode) {
            this.updateStock();
            this.updateWaste();
            this.updateFoundations();
            this.updateTableau();
            this.updateStats();
        }
    }

    updateStock() {
        const stockElement = $('#stock');
        stockElement.empty();

        if (this.stock.length > 0) {
            const cardBack = $('<div class="card-back"></div>');
            stockElement.append(cardBack);

            const countIndicator = $('<div class="stock-count"></div>');
            countIndicator.text(this.stock.length);
            countIndicator.css({
                position: 'absolute',
                top: '5px',
                right: '5px',
                background: 'rgba(0,0,0,0.7)',
                color: 'white',
                borderRadius: '10px',
                padding: '2px 6px',
                fontSize: '12px',
                fontWeight: 'bold',
                zIndex: 10
            });
            stockElement.append(countIndicator);

            stockElement.removeClass('empty');
        } else {
            stockElement.html('<div class="stock-empty">↻</div>');
            stockElement.addClass('empty');
        }
    }

    updateWaste() {
        const wasteElement = $('#waste');
        wasteElement.empty();

        if (this.waste.length > 0) {
            const topCard = this.waste[this.waste.length - 1];
            const cardElement = this.createCardElement(topCard);
            cardElement.addClass('top-card draggable-card');
            wasteElement.append(cardElement);
        }
    }

    updateFoundations() {
        for (let suit of this.suits) {
            const foundationElement = $(`#foundation-${suit}`);
            foundationElement.empty();

            if (this.foundations[suit].length > 0) {
                const topCard = this.foundations[suit][this.foundations[suit].length - 1];
                const cardElement = this.createCardElement(topCard);
                foundationElement.append(cardElement);
            } else {
                foundationElement.html(`<div class="pile-placeholder">${this.suitSymbols[suit]}</div>`);
            }
        }
    }

    updateTableau() {
        for (let i = 0; i < 7; i++) {
            const tableauElement = $(`#tableau-${i}`);
            tableauElement.empty();
            
                const spacing = window.innerWidth < 1024 ? 15 : 25;
            this.tableau[i].forEach((card, index) => {
                const cardElement = this.createCardElement(card);
                cardElement.css({
                    position: 'absolute',
                    top: `${index * spacing}px`,
                    zIndex: index + 1
                });

                if (card.faceUp) {
                    cardElement.addClass('draggable-card');
                }

                if (card.faceUp && index === this.tableau[i].length - 1) {
                    cardElement.addClass('top-card');
                }

                tableauElement.append(cardElement);
            });
        }
    }

    createCardElement(card) {
        const cardElement = $('<div>').addClass('card');

        if (!card.faceUp) {
            cardElement.addClass('face-down');
            return cardElement;
        }

        cardElement.addClass(card.color);
        cardElement.attr('data-suit', card.suit);
        cardElement.attr('data-rank', card.rank);
        cardElement.attr('data-value', card.value);

        const symbol = this.suitSymbols[card.suit];

        cardElement.html(`
            <div class="card-top">
                <span class="rank">${card.rank}</span>
                <span class="suit">${symbol}</span>
            </div>
            <div class="card-center">${symbol}</div>
            <div class="card-bottom">
                <span class="rank">${card.rank}</span>
                <span class="suit">${symbol}</span>
            </div>
        `);

        return cardElement;
    }

    updateStats() {
        $('#score').text(this.score);
        $('#moves').text(this.moves);
    }

    drawFromStock() {
        if (this.isAnimatingCard) return;

        if (this.stock.length === 0) {
            if (this.waste.length === 0) return;

            this.recordMove({
                type: 'recycle',
                wasteCards: [...this.waste]
            });

            this.stock = [...this.waste].reverse();
            this.stock.forEach(card => card.faceUp = false);
            this.waste = [];
            this.moves++;
            this.updateDisplay().then(() => {
                setTimeout(() => {
                    if (this.checkDeadlock()) {
                        this.showDeadlockMessage();
                    }
                }, 100);
            });
        } else {
            const card = this.stock.pop();

            this.recordMove({
                type: 'draw',
                count: 1,
                cards: [card]
            });

            this.moves++;
            this.isAnimatingCard = true;
            this.updateDisplay().then(() => {
                this.animateCardFlip(card);
            });
        }
    }

    onPointerDown(e) {
        if (e.type === 'mousedown' && (e.which !== 1 && e.button !== 0)) return;

        this.resetDragState();
        const coords = this.getEventCoordinates(e);
        this.dragStartPos = { x: coords.clientX, y: coords.clientY };

        const target = e.target || e.srcElement;

        if (target.closest('#stock') || $(target).closest('#stock').length) {
            e.preventDefault();
            e.stopPropagation();
            this.drawFromStock();
            return;
        }

        const cardElement = target.closest('.card') || $(target).closest('.card')[0];
        if (!cardElement) return;

        e.preventDefault();
        e.stopPropagation();

        const $cardElement = $(cardElement);

        if ($cardElement.hasClass('face-down')) {
            this.flipCardElement = $cardElement;
            return;
        }

        const cardRect = cardElement.getBoundingClientRect();
        this.dragOffset.x = coords.clientX - cardRect.left;
        this.dragOffset.y = coords.clientY - cardRect.top;

        this.originalCardPositions = [];
        this.prepareDragData($cardElement);

        const isMobile = e.type.startsWith('touch');
        const isWastePile = $cardElement.closest('.waste-pile').length > 0;

        if (isWastePile) {
            this.resetDragState();
            this.smartAutoMove($cardElement);
            return;
        }

        if (isMobile) {
            this.longPressTimer = setTimeout(() => {
                if (!this.isDragging && this.draggedCards && this.draggedCards.length > 0) {
                    navigator.vibrate && navigator.vibrate(50);
                }
            }, this.longPressDelay);
        }
    }

    prepareDragData(cardElement) {
        if (cardElement.closest('.waste-pile').length) {
            if (this.waste.length > 0) {
                const topCard = this.waste[this.waste.length - 1];
                this.draggedCards = [topCard];
            } else {
                this.draggedCards = [{
                    suit: cardElement.attr('data-suit'),
                    rank: cardElement.attr('data-rank'),
                    value: parseInt(cardElement.attr('data-value')),
                    color: cardElement.hasClass('red') ? 'red' : 'black',
                    faceUp: true
                }];
            }
            this.draggedFrom = { type: 'waste' };
            this.draggedElement = cardElement;
            this.draggedElements = [cardElement];

            const rect = cardElement[0].getBoundingClientRect();
            this.originalCardPositions.push({
                element: cardElement,
                left: rect.left,
                top: rect.top
            });

        } else if (cardElement.closest('.tableau-pile').length) {
            const pileIndex = parseInt(cardElement.closest('.tableau-pile').attr('id').split('-')[1]);
            const pile = this.tableau[pileIndex];
            const cardIndex = cardElement.index();

            this.draggedCards = [];
            for (let i = cardIndex; i < pile.length; i++) {
                if (pile[i].faceUp) {
                    this.draggedCards.push(pile[i]);
                } else {
                    break;
                }
            }

            this.draggedFrom = { type: 'tableau', index: pileIndex, startIndex: cardIndex };
            this.draggedElement = cardElement;
            this.draggedElements = [];

            const tableauElement = $(`#tableau-${pileIndex}`);
            for (let i = 0; i < this.draggedCards.length; i++) {
                const element = tableauElement.children().eq(cardIndex + i);
                this.draggedElements.push(element);

                const rect = element[0].getBoundingClientRect();
                this.originalCardPositions.push({
                    element: element,
                    left: rect.left,
                    top: rect.top
                });
            }

        } else if (cardElement.closest('.foundation-pile').length) {
            const suit = cardElement.closest('.foundation-pile').attr('id').split('-')[1];
            const foundationPile = this.foundations[suit];
            if (foundationPile.length > 0) {
                const topCard = foundationPile[foundationPile.length - 1];
                this.draggedCards = [topCard];
                this.draggedFrom = { type: 'foundation', suit: suit };
                this.draggedElement = cardElement;
                this.draggedElements = [cardElement];

                const rect = cardElement[0].getBoundingClientRect();
                this.originalCardPositions.push({
                    element: cardElement,
                    left: rect.left,
                    top: rect.top
                });
            }
        }
    }

    onPointerMove(e) {
        if (!this.draggedCards || this.draggedCards.length === 0) return;

        const coords = this.getEventCoordinates(e);

        if (!this.isDragging) {
            const distance = Math.sqrt(
                Math.pow(coords.clientX - this.dragStartPos.x, 2) +
                Math.pow(coords.clientY - this.dragStartPos.y, 2)
            );

            if (distance > this.dragThreshold) {
                this.isDragging = true;
                this.startDragging();

                const x = coords.clientX - this.dragOffset.x;
                const y = coords.clientY - this.dragOffset.y;

                if (this.draggedElements && this.draggedElements.length > 0) {
                    this.draggedElements.forEach((element, index) => {
                        const spacing = window.innerWidth < 1024 ? 10 : 25;
                        const cssProps = {
                            left: x + 'px',
                            top: (y + index * spacing) + 'px'
                        };

                        if (this.draggedFrom && this.draggedFrom.type === 'waste') {
                            cssProps.transform = 'none';
                        }

                        element.css(cssProps);
                    });
                }
                return;
            }
        }

        if (this.isDragging) {
            e.preventDefault();
            e.stopPropagation();

            if (e.type.startsWith('touch')) {
                document.body.style.overflow = 'hidden';
            }

            const x = coords.clientX - this.dragOffset.x;
            const y = coords.clientY - this.dragOffset.y;

            if (this.draggedElements && this.draggedElements.length > 0) {
                const spacing = window.innerWidth < 1024 ? 10 : 25;
                this.draggedElements.forEach((element, index) => {
                    const cssProps = {
                        left: x + 'px',
                        top: (y + index * spacing) + 'px'
                    };

                    if (this.draggedFrom && this.draggedFrom.type === 'waste') {
                        cssProps.transform = 'none';
                    }

                    element.css(cssProps);
                });
            }
        }
    }

    onPointerUp(e) {
        if (this.flipCardElement) {
            this.flipCard(this.flipCardElement);
            this.flipCardElement = null;
            this.resetDragState();
            return;
        }

        if (!this.isDragging) {
            if (this.draggedCards && this.draggedCards.length > 0 && this.draggedElement) {
                this.smartAutoMove(this.draggedElement);
            }
            this.resetDragState();
            return;
        }

        if (!this.draggedCards) {
            this.resetDragState();
            return;
        }

        const coords = this.getEventCoordinates(e);
        const detectRadius = 50;
        let dropped = false;

        for (let offsetX = -detectRadius; offsetX <= detectRadius; offsetX += detectRadius) {
            for (let offsetY = -detectRadius; offsetY <= detectRadius; offsetY += detectRadius) {
                const testX = coords.clientX + offsetX;
                const testY = coords.clientY + offsetY;
                const element = document.elementFromPoint(testX, testY);

                if (element) {
                    const $element = $(element);

                    const foundationPile = $element.closest('.foundation-pile');
                    if (foundationPile.length && this.draggedCards.length === 1 && this.draggedFrom.type !== 'foundation') {
                        const suit = foundationPile.attr('id').split('-')[1];
                        if (this.canMoveToFoundationByValue(suit, this.draggedCards[0].value)) {
                            dropped = this.moveToFoundation(suit);
                            if (dropped) break;
                        }
                    }

                    const tableauPile = $element.closest('.tableau-pile');
                    if (tableauPile.length) {
                        const pileIndex = parseInt(tableauPile.attr('id').split('-')[1]);

                        if (this.draggedFrom.type === 'foundation' && this.draggedCards.length === 1) {
                            if (this.canMoveToTableauPile(pileIndex, this.draggedCards[0])) {
                                dropped = this.moveFoundationToTableau(pileIndex);
                                if (dropped) break;
                            }
                        } else if (this.canMoveSequenceToTableauPile(pileIndex, this.draggedCards)) {
                            dropped = this.moveToTableau(pileIndex);
                            if (dropped) break;
                        }
                    }
                }
            }
            if (dropped) break;
        }

        if (!dropped) {
            this.animateCardsBackToOriginalPosition();
        } else {
            this.cleanupDrag();
        }

        this.justFinishedDrag = true;
        setTimeout(() => { this.justFinishedDrag = false; }, 100);

        if (dropped) {
            this.resetDragState();
        }
    }

    startDragging() {
        if (this.draggedElements && this.draggedElements.length > 0) {
            this.draggedElements.forEach((element, index) => {
                element.css({
                    'transition': 'none !important',
                    'transform': 'none !important',
                    'animation': 'none !important',
                    'transform-style': 'flat !important'
                });

                element[0].offsetHeight;

                const currentRect = element[0].getBoundingClientRect();

                element.addClass('dragging');
                element.css({
                    'position': 'fixed !important',
                    'left': currentRect.left + 'px',
                    'top': currentRect.top + 'px',
                    'z-index': (9999 - index) + ' !important',
                    'pointer-events': 'none !important',
                    'transition': 'none !important',
                    'transform': 'scale(1.05) !important',
                    'animation': 'none !important',
                    'transform-style': 'flat !important'
                });

                element[0].offsetHeight;
            });
        }
    }

    animateCardsBackToOriginalPosition() {
        if (!this.originalCardPositions || this.originalCardPositions.length === 0) {
            this.cleanupDrag();
            return;
        }

        this.originalCardPositions.forEach((pos) => {
            pos.element.addClass('card-returning');
            pos.element.css({
                'transition': 'all 0.3s ease-out',
                'left': pos.left + 'px',
                'top': pos.top + 'px'
            });
        });

        setTimeout(() => {
            this.originalCardPositions.forEach((pos) => {
                pos.element.removeClass('card-returning');
            });
            this.cleanupDrag();

            if (this.draggedElement) {
                this.showNoMoveAvailable(this.draggedElement);
            }

            this.resetDragState();
        }, 300);
    }

    cleanupDrag() {
        $('.card').removeClass('dragging card-returning');
        $('.card').css({
            left: '',
            top: '',
            position: '',
            'z-index': '',
            'pointer-events': '',
            'transition': '',
            'transform': '',
            'animation': '',
            'transform-style': ''
        });

        document.body.style.overflow = '';
        this.updateDisplay();
    }

    isValidTableauSequence(cards) {
        if (cards.length <= 1) return true;

        for (let i = 1; i < cards.length; i++) {
            const prevCard = cards[i - 1];
            const currentCard = cards[i];

            if (prevCard.color === currentCard.color || prevCard.value !== currentCard.value + 1) {
                return false;
            }
        }
        return true;
    }

    showHelp() {
        $('#helpPanel').removeClass('hidden');
    }

    hideHelp() {
        $('#helpPanel').addClass('hidden');
    }

    smartAutoMove(cardElement) {
        if (this.isAnimatingCard || this.isAutoCompleting) {
            return;
        }

        const card = {
            suit: cardElement.attr('data-suit'),
            rank: cardElement.attr('data-rank'),
            value: parseInt(cardElement.attr('data-value')),
            color: cardElement.hasClass('red') ? 'red' : 'black',
            faceUp: true
        };

        if (cardElement.closest('.foundation-pile').length) {
            const suit = cardElement.closest('.foundation-pile').attr('id').split('-')[1];
            const foundationPile = this.foundations[suit];

            if (foundationPile.length === 0 || foundationPile[foundationPile.length - 1].value !== card.value) {
                this.showNoMoveAvailable(cardElement);
                return;
            }

            const bestTableauIndex = this.findBestTableauPosition(card);
            if (bestTableauIndex !== -1) {
                this.moveFoundationCardToTableau(cardElement, card, bestTableauIndex);
                return;
            }
            this.showNoMoveAvailable(cardElement);
            return;
        }

        if (cardElement.closest('.waste-pile').length) {
            if (!cardElement.hasClass('top-card')) {
                this.showNoMoveAvailable(cardElement);
                return;
            }
            const bestTableauIndex = this.findBestTableauPosition(card);
            if (bestTableauIndex !== -1) {
                this.moveCardToTableau(cardElement, card, bestTableauIndex);
                return;
            }

            if (this.canMoveToFoundation(card)) {
                this.moveCardToFoundation(cardElement, card);
                return;
            }
            this.showNoMoveAvailable(cardElement);
            return;
        }

        if (cardElement.closest('.tableau-pile').length) {
            const pile = cardElement.closest('.tableau-pile');
            const cards = pile.find('.card');
            if (!cardElement.is(cards.last()) || cardElement.hasClass('face-down')) {
                this.showNoMoveAvailable(cardElement);
                return;
            }

            if (this.canMoveToFoundation(card)) {
                this.moveCardToFoundation(cardElement, card);
                return;
            }

            const bestTableauIndex = this.findBestTableauPosition(card);
            if (bestTableauIndex !== -1) {
                this.moveCardToTableau(cardElement, card, bestTableauIndex);
                return;
            }
        }
        this.showNoMoveAvailable(cardElement);
    }

    canMoveToFoundation(card) {
        if (!card || !card.suit) {
            console.warn('canMoveToFoundation: Invalid card', card);
            return false;
        }
        const foundationPile = this.foundations[card.suit];
        if (!foundationPile) {
            console.warn('canMoveToFoundation: Invalid suit', card.suit);
            return false;
        }
        if (foundationPile.length === 0) {
            return card.rank === 'A';
        } else {
            const topCard = foundationPile[foundationPile.length - 1];
            return card.value === topCard.value + 1;
        }
    }

    moveCardToFoundation(cardElement, card) {
        const targetFoundation = $(`#foundation-${card.suit}`);

        this.animateCardFly(cardElement, targetFoundation, () => {
            this.executeFoundationMove(cardElement, card);
        });
    }

    moveFoundationCardToTableau(cardElement, card, tableauIndex) {
        const targetTableau = $(`#tableau-${tableauIndex}`);

        this.animateCardFly(cardElement, targetTableau, () => {
            this.executeFoundationToTableauMove(card, tableauIndex);
        });
    }

    executeFoundationToTableauMove(card, tableauIndex) {
        const suit = card.suit;
        const foundationPile = this.foundations[suit];

        if (foundationPile.length === 0 || foundationPile[foundationPile.length - 1].value !== card.value) {
            console.error('Invalid foundation to tableau move: card not on top of foundation');
            return false;
        }

        if (!this.canMoveToTableauPile(tableauIndex, card)) {
            console.error('Invalid foundation to tableau move: cannot place on tableau');
            return false;
        }

        const movedCard = foundationPile.pop();
        this.tableau[tableauIndex].push(movedCard);

        const moveData = {
            type: 'move',
            from: { type: 'foundation', suit: suit },
            to: { type: 'tableau', index: tableauIndex },
            card: movedCard
        };

        this.recordMove(moveData);
        this.moves++;
        this.score -= 15;
        this.updateDisplay();

        return true;
    }

    moveFoundationToTableau(tableauIndex) {
        if (!this.draggedCards || this.draggedCards.length !== 1 || this.draggedFrom.type !== 'foundation') {
            return false;
        }

        const card = this.draggedCards[0];
        const suit = this.draggedFrom.suit;
        const foundationPile = this.foundations[suit];

        if (foundationPile.length === 0 || foundationPile[foundationPile.length - 1].value !== card.value) {
            console.error('Invalid foundation to tableau drag: card not on top of foundation');
            return false;
        }

        if (!this.canMoveToTableauPile(tableauIndex, card)) {
            console.error('Invalid foundation to tableau drag: cannot place on tableau');
            return false;
        }

        const movedCard = foundationPile.pop();
        this.tableau[tableauIndex].push(movedCard);

        const moveData = {
            type: 'move',
            from: { type: 'foundation', suit: suit },
            to: { type: 'tableau', index: tableauIndex },
            card: movedCard
        };

        this.recordMove(moveData);
        this.moves++;
        this.score -= 15;
        this.updateDisplay();

        return true;
    }

    executeFoundationMove(cardElement, card) {
        if (!this.canMoveToFoundation(card)) {
            console.error('Invalid foundation move attempted:', card, 'Foundation state:', this.foundations[card.suit]);
            return false;
        }

        let moveData = {
            type: 'move',
            to: { type: 'foundation', suit: card.suit, pile: this.foundations[card.suit] },
            card: card
        };

        if (cardElement.closest('.waste-pile').length) {
            moveData.from = { type: 'waste', pile: this.waste };
            this.waste.pop();
        } else if (cardElement.closest('.tableau-pile').length) {
            const pileIndex = parseInt(cardElement.closest('.tableau-pile').attr('id').split('-')[1]);
            const pile = this.tableau[pileIndex];
            moveData.from = { type: 'tableau', index: pileIndex, pile: pile };
            pile.pop();

            if (pile.length > 0 && !pile[pile.length - 1].faceUp) {
                pile[pile.length - 1].faceUp = true;
                this.score += 5;
                moveData.flippedCard = pile[pile.length - 1];
            }
        }

        this.foundations[card.suit].push(card);
        this.score += 10;

        this.recordMove(moveData);
        this.moves++;

        this.updateDisplay();
        this.checkWinCondition();

        setTimeout(() => {
            if (!this.gameWon && this.checkDeadlock()) {
                this.showDeadlockMessage();
            } else if (!this.isAutoCompleting) {
                this.checkAutoCompleteTrigger();
            }
        }, 100);

        return true;
    }

    findBestTableauPosition(card) {
        for (let i = 0; i < this.tableau.length; i++) {
            const pile = this.tableau[i];
            if (pile.length === 0) {
                if (card.rank === 'K') return i;
            } else {
                const topCard = pile[pile.length - 1];
                if (topCard.faceUp &&
                    topCard.value === card.value + 1 &&
                    topCard.color !== card.color) {
                    return i;
                }
            }
        }
        return -1;
    }

    moveCardToTableau(cardElement, card, tableauIndex) {
        const targetTableau = $(`#tableau-${tableauIndex}`);

        this.animateCardFly(cardElement, targetTableau, () => {
            this.executeTableauMove(cardElement, card, tableauIndex);
        });
    }

    executeTableauMove(cardElement, card, tableauIndex) {
        let moveData = {
            type: 'move',
            to: { type: 'tableau', index: tableauIndex, pile: this.tableau[tableauIndex] },
            card: card
        };

        if (cardElement.closest('.waste-pile').length) {
            moveData.from = { type: 'waste', pile: this.waste };
            this.waste.pop();
        } else if (cardElement.closest('.tableau-pile').length) {
            const pileIndex = parseInt(cardElement.closest('.tableau-pile').attr('id').split('-')[1]);
            const pile = this.tableau[pileIndex];
            moveData.from = { type: 'tableau', index: pileIndex, pile: pile };
            pile.pop();

            if (pile.length > 0 && !pile[pile.length - 1].faceUp) {
                pile[pile.length - 1].faceUp = true;
                this.score += 5;
                moveData.flippedCard = pile[pile.length - 1];
            }
        }

        this.tableau[tableauIndex].push(card);

        this.recordMove(moveData);
        this.moves++;
        this.updateDisplay();

        setTimeout(() => {
            if (!this.gameWon && this.checkDeadlock()) {
                this.showDeadlockMessage();
            } else if (!this.isAutoCompleting) {
                this.checkAutoCompleteTrigger();
            }
        }, 100);

        return true;
    }

    removeCardFromSource(cardElement) {
        if (cardElement.closest('.waste-pile').length) {
            this.waste.pop();
        } else if (cardElement.closest('.tableau-pile').length) {
            const pileIndex = parseInt(cardElement.closest('.tableau-pile').attr('id').split('-')[1]);
            this.tableau[pileIndex].pop();

            const pile = this.tableau[pileIndex];
            if (pile.length > 0 && !pile[pile.length - 1].faceUp) {
                pile[pile.length - 1].faceUp = true;
            }
        } else if (cardElement.closest('.foundation-pile').length) {
            const suit = cardElement.closest('.foundation-pile').attr('id').split('-')[1];
            this.foundations[suit].pop();
        }
    }

    showNoMoveAvailable(cardElement) {
        cardElement.addClass('no-move-available');
        setTimeout(() => {
            cardElement.removeClass('no-move-available');
        }, 300);
    }

    flipCard(cardElement) {
        const tableauPile = cardElement.closest('.tableau-pile');
        if (tableauPile.length) {
            const pileIndex = parseInt(tableauPile.attr('id').split('-')[1]);
            const pile = this.tableau[pileIndex];
            const cardIndex = cardElement.index();

            if (cardIndex >= 0 && cardIndex < pile.length &&
                !pile[cardIndex].faceUp && cardIndex === pile.length - 1) {

                this.animateTableauCardFlip(cardElement, () => {
                    pile[cardIndex].faceUp = true;
                    this.score += 5;
                    this.moves++;
                    this.updateDisplay().then(() => {
                        this.checkAutoCompleteTrigger();
                    });
                });
            }
        }
    }

    animateCardFlip(card) {
        this.animateBookFlip(card);
    }

    animateBookFlip(card) {
        const cardBackElement = $('#stock .card-back');
        if (cardBackElement.length > 0) {
            const tempCardElement = this.createCardElement(card);
            tempCardElement.addClass('book-flip book-flip-start');
            tempCardElement.css({
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%'
            });

            cardBackElement.css('opacity', '0');
            $('#stock').append(tempCardElement);

            setTimeout(() => {
                tempCardElement.removeClass('book-flip-start').addClass('book-flip-middle');

                setTimeout(() => {
                    tempCardElement.removeClass('book-flip-middle').addClass('book-flip-end');

                    setTimeout(() => {
                        tempCardElement.remove();
                        cardBackElement.css('opacity', '1');

                        card.faceUp = true;
                        this.waste.push(card);
                        this.updateDisplay();

                        this.isAnimatingCard = false;
                    }, 150);
                }, 150);
            }, 10);
        } else {
            card.faceUp = true;
            this.waste.push(card);
            this.updateDisplay();
            this.isAnimatingCard = false;
        }
    }

    animateTableauCardFlip(cardElement, callback) {
        cardElement.addClass('flipping flip-start');

        setTimeout(() => {
            cardElement.removeClass('flip-start').addClass('flip-middle');

            setTimeout(() => {
                callback();

                setTimeout(() => {
                    cardElement.removeClass('flip-middle').addClass('flip-end');

                    setTimeout(() => {
                        cardElement.removeClass('flipping flip-end');
                    }, 150);
                }, 10);
            }, 150);
        }, 10);
    }

    autoMoveCard(cardElement) {
        const suit = cardElement.attr('data-suit');
        const value = parseInt(cardElement.attr('data-value'));
        const rank = cardElement.attr('data-rank');

        const card = {
            suit: suit,
            rank: rank,
            value: value,
            color: cardElement.hasClass('red') ? 'red' : 'black',
            faceUp: true
        };

        if (this.canMoveToFoundation(card)) {
            this.draggedCards = [card];

            if (cardElement.closest('.waste-pile').length) {
                this.draggedFrom = { type: 'waste' };
            } else if (cardElement.closest('.tableau-pile').length) {
                const pileIndex = parseInt(cardElement.closest('.tableau-pile').attr('id').split('-')[1]);
                const pile = this.tableau[pileIndex];
                const cardIndex = cardElement.index();

                if (cardIndex === pile.length - 1) {
                    this.draggedFrom = { type: 'tableau', index: pileIndex };
                } else {
                    return;
                }
            }

            const targetFoundation = $(`#foundation-${suit}`);

            this.animateCardFly(cardElement, targetFoundation, () => {
                if (this.moveToFoundation(suit)) {
                    this.updateDisplay();
                }
            });
        }
    }

    animateCardFly(cardElement, targetElement, callback) {
        if (this.isAnimatingCard) return;
        this.isAnimatingCard = true;
        const startRect = cardElement[0].getBoundingClientRect();
        let targetRect;
        const lastCard = targetElement.find('.card').last();
        if (lastCard.length > 0) {
            const lastRect = lastCard[0].getBoundingClientRect();
            targetRect = {
                left: lastRect.left,
                top: lastRect.top,
                width: lastRect.width,
                height: lastRect.height
            };
        } else {
            const containerRect = targetElement[0].getBoundingClientRect();
            targetRect = {
                left: containerRect.left,
                top: containerRect.top,
                width: containerRect.width,
                height: containerRect.height
            };
        }

        const flyingCard = cardElement.clone();
        flyingCard.addClass('flying');
        flyingCard.css({
            position: 'fixed',
            left: startRect.left + 'px',
            top: startRect.top + 'px',
            width: startRect.width + 'px',
            height: startRect.height + 'px',
            zIndex: 10000
        });

        $('body').append(flyingCard);
        cardElement.css('opacity', '0');

        setTimeout(() => {
            flyingCard.css({
                left: targetRect.left + 'px',
                top: targetRect.top + 'px'
            });
        }, 10);

        setTimeout(() => {
            flyingCard.remove();
            cardElement.css('opacity', '1');
            this.isAnimatingCard = false;
            if (callback) callback();
        }, 250);
    }

    animateCardFlyFast(cardElement, targetElement, callback) {
        if (this.testMode) {
            if (callback) callback();
            return;
        }

        if (!cardElement || !cardElement.length) {
            if (callback) callback();
            return;
        }

        const startRect = cardElement[0].getBoundingClientRect();
        let targetRect;

        const lastCard = targetElement.find('.card').last();
        if (lastCard.length > 0) {
            const lastRect = lastCard[0].getBoundingClientRect();
            targetRect = {
                left: lastRect.left,
                top: lastRect.top,
                width: lastRect.width,
                height: lastRect.height
            };
        } else {
            const containerRect = targetElement[0].getBoundingClientRect();
            targetRect = {
                left: containerRect.left,
                top: containerRect.top,
                width: containerRect.width,
                height: containerRect.height
            };
        }

        const flyingCard = cardElement.clone();
        flyingCard.addClass('flying-fast');
        flyingCard.css({
            position: 'fixed',
            left: startRect.left + 'px',
            top: startRect.top + 'px',
            width: startRect.width + 'px',
            height: startRect.height + 'px',
            zIndex: 10000,
            transition: 'all 0.08s cubic-bezier(0.25, 0.46, 0.45, 0.94)'
        });

        $('body').append(flyingCard);
        cardElement.css('opacity', '0');

        setTimeout(() => {
            flyingCard.css({
                left: targetRect.left + 'px',
                top: targetRect.top + 'px'
            });
        }, 5);

        setTimeout(() => {
            flyingCard.remove();
            cardElement.css('opacity', '1');
            if (callback) callback();
        }, 80);
    }

    showMoveAnimation(cardElement, suit = null) {
        if (suit) {
            cardElement.addClass('moving-to-foundation');
        } else {
            cardElement.addClass('moving-to-tableau');
        }
        setTimeout(() => {
            cardElement.removeClass('moving-to-foundation moving-to-tableau');
        }, 150);
    }

    canMoveToFoundationByValue(suit, value) {
        if (!suit || !this.foundations[suit]) {
            console.warn('canMoveToFoundationByValue: Invalid suit', suit);
            return false;
        }
        if (typeof value !== 'number' || value < 1 || value > 13) {
            console.warn('canMoveToFoundationByValue: Invalid value', value);
            return false;
        }
        const foundation = this.foundations[suit];
        return foundation.length === 0 ? value === 1 : foundation[foundation.length - 1].value === value - 1;
    }

    async moveToFoundation(suit) {
        if (!this.draggedCards || this.draggedCards.length !== 1 || !this.canMoveToFoundationByValue(suit, this.draggedCards[0].value)) {
            return false;
        }

        if (!this.draggedFrom || !this.draggedFrom.type) {
            console.error('Invalid draggedFrom state:', this.draggedFrom);
            return false;
        }

        const card = this.draggedCards[0];
        let cardElement;
        if (this.draggedFrom.type === 'waste') {
            cardElement = $('.waste-pile .card').last();
        } else if (this.draggedFrom.type === 'tableau') {
            cardElement = $(`#tableau-${this.draggedFrom.index} .card`).last();
        }
        const targetElement = $(`#foundation-${suit}`);
        await new Promise(resolve => {
            this.animateCardFly(cardElement, targetElement, () => {
                let moveData = {
                    type: 'move',
                    to: { type: 'foundation', suit: suit, pile: this.foundations[suit] },
                    card: card
                };
                if (this.draggedFrom.type === 'waste' && this.waste.length > 0) {
                    const c = this.waste.pop();
                    if (!this.canMoveToFoundationByValue(suit, c.value)) {
                        console.error('Invalid waste to foundation move:', c, 'Foundation state:', this.foundations[suit]);
                        this.waste.push(c);
                        return false;
                    }
                    c.faceUp = true;
                    this.foundations[suit].push(c);
                    this.score += 10;
                    moveData.from = { type: 'waste', pile: this.waste };
                } else if (this.draggedFrom.type === 'tableau') {
                    const pile = this.tableau[this.draggedFrom.index];
                    if (pile.length > 0) {
                        const c = pile.pop();
                        if (!this.canMoveToFoundationByValue(suit, c.value)) {
                            console.error('Invalid tableau to foundation move:', c, 'Foundation state:', this.foundations[suit]);
                            pile.push(c);
                            return false;
                        }
                        c.faceUp = true;
                        this.foundations[suit].push(c);
                        this.score += 10;
                        moveData.from = { type: 'tableau', index: this.draggedFrom.index, pile: pile };
                        if (pile.length > 0 && !pile[pile.length - 1].faceUp) {
                            pile[pile.length - 1].faceUp = true;
                            this.score += 5;
                            moveData.flippedCard = pile[pile.length - 1];
                        }
                    }
                }
                this.recordMove(moveData);
                this.moves++;
                this.updateDisplay();
                this.checkWinCondition();

                setTimeout(() => {
                    if (!this.gameWon && this.checkDeadlock()) {
                        this.showDeadlockMessage();
                    } else if (!this.isAutoCompleting) {
                        this.checkAutoCompleteTrigger();
                    }
                }, 100);
                resolve();
            });
        });
        return true;
    }

    async moveToTableau(pileIndex) {
        if (!this.draggedCards || this.draggedCards.length === 0) return false;
        const targetPile = this.tableau[pileIndex];
        const firstCard = this.draggedCards[0];
        if (targetPile.length === 0) {
            if (firstCard.value !== 13) return false;
        } else {
            const topCard = targetPile[targetPile.length - 1];
            if (!topCard.faceUp || topCard.color === firstCard.color || topCard.value !== firstCard.value + 1) {
                return false;
            }
        }
        if (this.draggedCards.length > 1 && !this.isValidTableauSequence(this.draggedCards)) {
            return false;
        }
        let moveData = {
            type: 'move',
            to: { type: 'tableau', index: pileIndex, pile: targetPile },
            cards: [...this.draggedCards]
        };
        if (this.draggedFrom.type === 'waste' && this.waste.length > 0) {
            const card = this.waste.pop();
            card.faceUp = true;
            targetPile.push(card);
            moveData.from = { type: 'waste', pile: this.waste };
        } else if (this.draggedFrom.type === 'tableau') {
            const sourcePile = this.tableau[this.draggedFrom.index];
            const cardsToMove = sourcePile.splice(this.draggedFrom.startIndex);
            cardsToMove.forEach(card => card.faceUp = true);
            targetPile.push(...cardsToMove);
            moveData.from = { type: 'tableau', index: this.draggedFrom.index, pile: sourcePile, startIndex: this.draggedFrom.startIndex };
            if (sourcePile.length > 0 && !sourcePile[sourcePile.length - 1].faceUp) {
                sourcePile[sourcePile.length - 1].faceUp = true;
                this.score += 5;
                moveData.flippedCard = sourcePile[sourcePile.length - 1];
            }
        }
        this.recordMove(moveData);
        this.moves++;
        await this.updateDisplay();
        setTimeout(() => {
            if (this.checkDeadlock()) {
                this.showDeadlockMessage();
            }
        }, 100);
        return true;
    }

    checkWinCondition() {
        const totalFoundationCards = Object.values(this.foundations).reduce((sum, pile) => sum + pile.length, 0);

        if (totalFoundationCards === 52) {
            this.gameWon = true;
            this.stopTimer();
            this.showWinMessage();
        }
    }

    checkDeadlock() {
        if (this.gameWon) return false;

        const possibleMoves = this.findBasicMoves();

        if (possibleMoves.length > 0) return false;

        if (this.stock.length > 0) {
            return false;
        }

        let faceDownCount = 0;
        for (let i = 0; i < 7; i++) {
            const pile = this.tableau[i];
            for (let j = 0; j < pile.length; j++) {
                if (!pile[j].faceUp) {
                    faceDownCount++;
                }
            }
        }

        if (faceDownCount > 0) {
            return false;
        }

        return true;
    }

    showDeadlockMessage() {
        const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
        const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
        const seconds = (elapsed % 60).toString().padStart(2, '0');

        $('#messageTitle').text('Game Over');
        $('#messageText').text('No more moves available. The game cannot continue.');
        $('#finalScore').text(this.score);
        $('#finalTime').text(`${minutes}:${seconds}`);
        $('#finalMoves').text(this.moves);

        $('#playAgainBtn').text('New Game').off('click').on('click', () => {
            this.hideMessage();
            this.newGame();
        });

        $('#closeMessageBtn').text('Back to Home').off('click').on('click', () => {
            window.location.href = '/';
        });

        $('#gameMessage').removeClass('hidden');
    }

    showWinMessage() {
        const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
        const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
        const seconds = (elapsed % 60).toString().padStart(2, '0');

        $('#messageTitle').text('Congratulations!');
        $('#messageText').text('You won the game!');
        $('#finalScore').text(this.score);
        $('#finalTime').text(`${minutes}:${seconds}`);
        $('#finalMoves').text(this.moves);

        $('#playAgainBtn').text('Play Again').off('click').on('click', () => {
            this.hideMessage();
            this.newGame();
        });

        $('#closeMessageBtn').text('Back to Home').off('click').on('click', () => {
            window.location.href = '/';
        });

        $('#gameMessage').removeClass('hidden');
    }

    hideMessage() {
        $('#gameMessage').addClass('hidden');
    }



    undoMove() {
        if (this.moveHistory.length === 0) return;

        const lastMove = this.moveHistory.pop();

        if (lastMove.type === 'draw') {
            for (let i = 0; i < lastMove.count; i++) {
                if (this.waste.length > 0) {
                    const card = this.waste.pop();
                    card.faceUp = false;
                    this.stock.push(card);
                }
            }
        } else if (lastMove.type === 'recycle') {
            this.waste = [...lastMove.wasteCards];
            this.stock = [];
        } else if (lastMove.type === 'move') {
            if (lastMove.cards) {
                const cardsToMove = lastMove.to.pile.splice(-lastMove.cards.length);
                if (lastMove.from && lastMove.from.pile) {
                    if (lastMove.from.startIndex !== undefined) {
                        lastMove.from.pile.splice(lastMove.from.startIndex, 0, ...cardsToMove);
                    } else {
                        lastMove.from.pile.push(...cardsToMove);
                    }
                }
            } else if (lastMove.card) {
                const card = lastMove.to.pile.pop();
                if (lastMove.from && lastMove.from.pile) {
                    lastMove.from.pile.push(card);
                }
            }

            if (lastMove.flippedCard) {
                lastMove.flippedCard.faceUp = false;
                this.score -= 5;
            }

            if (lastMove.to.type === 'foundation') {
                this.score -= 10;
            }
        }

        this.moves = Math.max(0, this.moves - 1);
        this.updateDisplay();
    }
    showKlondikeHint() {
        this.clearHintHighlights();

        const hint = this.findMeaningfulMove();

        if (!hint) {
            this.handleNoMovesAvailable();
            return;
        }

        this.displayHint(hint);

        setTimeout(() => {
            this.clearHintHighlights();
        }, 2000);
    }

    findMeaningfulMove() {
        let move = this.findAcesToFoundation();
        if (move) {
            return move;
        }

        move = this.findCardsToFoundation();
        if (move) {
            return move;
        }

        move = this.findMovesToRevealCards();
        if (move) {
            return move;
        }

        move = this.findKingsToEmptyColumns();
        if (move) {
            return move;
        }

        move = this.findTableauMoves();
        if (move) {
            return move;
        }

        move = this.findStockMove();
        if (move) {
            return move;
        }

        return null;
    }

    findAcesToFoundation() {
        if (this.waste.length > 0) {
            const wasteCard = this.waste[this.waste.length - 1];
            if (wasteCard.faceUp && wasteCard.rank === 'A') {
                const foundation = this.foundations[wasteCard.suit];
                if (foundation.length === 0) {
                    return {
                        type: 'ace-to-foundation',
                        priority: 1,
                        from: { type: 'waste', card: wasteCard },
                        to: { type: 'foundation', suit: wasteCard.suit },
                        description: `Move ${wasteCard.suit} A to Foundation to start new pile`
                    };
                }
            }
        }

        for (let i = 0; i < 7; i++) {
            const pile = this.tableau[i];
            if (pile.length > 0) {
                const topCard = pile[pile.length - 1];
                if (topCard.faceUp && topCard.rank === 'A') {
                    const foundation = this.foundations[topCard.suit];
                    if (foundation.length === 0) {
                        return {
                            type: 'ace-to-foundation',
                            priority: 1,
                            from: { type: 'tableau', index: i, card: topCard },
                            to: { type: 'foundation', suit: topCard.suit },
                            description: `Move ${topCard.suit} A from column ${i + 1} to Foundation`
                        };
                    }
                }
            }
        }

        return null;
    }
    findCardsToFoundation() {
        if (this.waste.length > 0) {
            const wasteCard = this.waste[this.waste.length - 1];
            if (wasteCard.faceUp && this.canMoveToFoundationByValue(wasteCard.suit, wasteCard.value)) {
                return {
                    type: 'card-to-foundation',
                    priority: 2,
                    from: { type: 'waste', card: wasteCard },
                    to: { type: 'foundation', suit: wasteCard.suit },
                    description: `Move ${wasteCard.rank} ${wasteCard.suit} to Foundation`
                };
            }
        }

        for (let i = 0; i < 7; i++) {
            const pile = this.tableau[i];
            if (pile.length > 0) {
                const topCard = pile[pile.length - 1];
                if (topCard.faceUp && this.canMoveToFoundationByValue(topCard.suit, topCard.value)) {
                    return {
                        type: 'card-to-foundation',
                        priority: 2,
                        from: { type: 'tableau', index: i, card: topCard },
                        to: { type: 'foundation', suit: topCard.suit },
                        description: `Move ${topCard.rank} ${topCard.suit} from column ${i + 1} to Foundation`
                    };
                }
            }
        }

        return null;
    }

    findMovesToRevealCards() {
        for (let i = 0; i < 7; i++) {
            const pile = this.tableau[i];
            if (pile.length === 0) continue;

            let hasHiddenCards = false;
            for (let j = 0; j < pile.length - 1; j++) {
                if (!pile[j].faceUp) {
                    hasHiddenCards = true;
                    break;
                }
            }

            if (!hasHiddenCards) continue;

            const topCard = pile[pile.length - 1];
            if (!topCard.faceUp) continue;

            if (this.canMoveToFoundationByValue(topCard.suit, topCard.value)) {
                return {
                    type: 'reveal-card',
                    priority: 3,
                    from: { type: 'tableau', index: i, card: topCard },
                    to: { type: 'foundation', suit: topCard.suit },
                    description: `Move ${topCard.rank} ${topCard.suit} to Foundation to reveal hidden card in column ${i + 1}`
                };
            }

            for (let j = 0; j < 7; j++) {
                if (i === j) continue;

                if (this.canMoveToTableauPile(j, topCard)) {
                    return {
                        type: 'reveal-card',
                        priority: 3,
                        from: { type: 'tableau', index: i, card: topCard },
                        to: { type: 'tableau', index: j },
                        description: `Move ${topCard.rank} ${topCard.suit} from column ${i + 1} to column ${j + 1} to reveal hidden card`
                    };
                }
            }
        }

        return null;
    }

    findKingsToEmptyColumns() {
        const emptyColumns = [];
        for (let i = 0; i < 7; i++) {
            if (this.tableau[i].length === 0) {
                emptyColumns.push(i);
            }
        }

        if (emptyColumns.length === 0) return null;

        if (this.waste.length > 0) {
            const wasteCard = this.waste[this.waste.length - 1];
            if (wasteCard.faceUp && wasteCard.rank === 'K') {
                return {
                    type: 'king-to-empty',
                    priority: 4,
                    from: { type: 'waste', card: wasteCard },
                    to: { type: 'tableau', index: emptyColumns[0] },
                    description: `Move K ${wasteCard.suit} to empty column ${emptyColumns[0] + 1} to start new sequence`
                };
            }
        }

        for (let i = 0; i < 7; i++) {
            const pile = this.tableau[i];
            if (pile.length === 0) continue;

            const topCard = pile[pile.length - 1];
            if (topCard.faceUp && topCard.rank === 'K') {
                let hasHiddenCards = false;
                for (let j = 0; j < pile.length - 1; j++) {
                    if (!pile[j].faceUp) {
                        hasHiddenCards = true;
                        break;
                    }
                }

                if (hasHiddenCards) {
                    return {
                        type: 'king-to-empty',
                        priority: 4,
                        from: { type: 'tableau', index: i, card: topCard },
                        to: { type: 'tableau', index: emptyColumns[0] },
                        description: `Move K ${topCard.suit} from column ${i + 1} to empty column ${emptyColumns[0] + 1} to reveal hidden card`
                    };
                }
            }
        }

        return null;
    }
    findTableauMoves() {
        if (this.waste.length > 0) {
            const wasteCard = this.waste[this.waste.length - 1];
            if (wasteCard.faceUp) {
                for (let i = 0; i < 7; i++) {
                    if (this.canMoveToTableauPile(i, wasteCard)) {
                        if (this.isMeaningfulTableauMove(wasteCard, 'waste', -1, i)) {
                            return {
                                type: 'tableau-move',
                                priority: 5,
                                from: { type: 'waste', card: wasteCard },
                                to: { type: 'tableau', index: i },
                                description: `Move ${wasteCard.rank} ${wasteCard.suit} from waste to column ${i + 1}`
                            };
                        }
                    }
                }
            }
        }

        for (let i = 0; i < 7; i++) {
            const pile = this.tableau[i];
            if (pile.length === 0) continue;

            let topFaceUpIndex = -1;
            for (let j = pile.length - 1; j >= 0; j--) {
                if (pile[j].faceUp) {
                    topFaceUpIndex = j;
                    break;
                }
            }

            if (topFaceUpIndex === -1) continue;

            for (let start = topFaceUpIndex; start < pile.length; start++) {
                const sequence = pile.slice(start);
                if (!this.isValidTableauSequence(sequence)) continue;

                const firstCard = sequence[0];

                for (let j = 0; j < 7; j++) {
                    if (i === j) continue;

                    if (this.canMoveSequenceToTableauPile(j, sequence)) {
                        if (this.isMeaningfulTableauMove(firstCard, 'tableau', i, j)) {
                            return {
                                type: 'tableau-move',
                                priority: 5,
                                from: { type: 'tableau', index: i, card: firstCard, sequence: sequence },
                                to: { type: 'tableau', index: j },
                                description: sequence.length === 1
                                    ? `Move ${firstCard.rank} ${firstCard.suit} from column ${i + 1} to column ${j + 1}`
                                    : `Move ${sequence.length} card sequence from column ${i + 1} to column ${j + 1}`
                            };
                        }
                    }
                }
            }
        }

        return null;
    }

    findStockMove() {
        if (this.stock.length > 0) {
            return {
                type: 'stock-move',
                priority: 6,
                from: { type: 'stock' },
                to: { type: 'waste' },
                description: 'Draw new card from stock to waste pile'
            };
        }

        if (this.stock.length === 0 && this.waste.length > 0) {
            return {
                type: 'reset-stock',
                priority: 6,
                from: { type: 'waste' },
                to: { type: 'stock' },
                description: 'Reset stock pile (flip waste pile back to stock)'
            };
        }

        return null;
    }

    isMeaningfulTableauMove(card, fromType, fromIndex, toIndex) {
        if (fromType === 'tableau' && fromIndex === toIndex) {
            return false;
        }

        const targetPile = this.tableau[toIndex];

        if (targetPile.length === 0) {
            return card.rank === 'K';
        }

        if (fromType === 'tableau') {
            const sourcePile = this.tableau[fromIndex];
            for (let i = 0; i < sourcePile.length - 1; i++) {
                if (!sourcePile[i].faceUp) {
                    return true;
                }
            }
        }

        const targetTopCard = targetPile[targetPile.length - 1];
        if (targetTopCard.value === card.value + 1 && targetTopCard.color !== card.color) {
            return true;
        }

        return false;
    }
    displayHint(hint) {
        this.highlightHintElements(hint);
        this.showHintMessage(hint.description);
    }

    highlightHintElements(hint) {
        if (hint.from.type === 'waste') {
            $('.waste-pile .card').last().addClass('card-hint');
        } else if (hint.from.type === 'tableau') {
            const pile = $(`#tableau-${hint.from.index}`);
            if (hint.from.sequence && hint.from.sequence.length > 1) {
                const cards = pile.find('.card');
                const startIndex = cards.length - hint.from.sequence.length;
                for (let i = startIndex; i < cards.length; i++) {
                    $(cards[i]).addClass('card-hint');
                }
            } else {
                pile.find('.card').last().addClass('card-hint');
            }
        } else if (hint.from.type === 'stock') {
            $('.stock-pile').addClass('pile-hint');
        }

        if (hint.to.type === 'foundation') {
            $(`#foundation-${hint.to.suit}`).addClass('pile-hint');
        } else if (hint.to.type === 'tableau') {
            $(`#tableau-${hint.to.index}`).addClass('pile-hint');
        } else if (hint.to.type === 'waste') {
            $('.waste-pile').addClass('pile-hint');
        } else if (hint.to.type === 'stock') {
            $('.stock-pile').addClass('pile-hint');
        }
    }

    showHintMessage(message) {
        const hintDiv = $('<div>').addClass('hint-message').text(message);
        hintDiv.css({
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            background: 'rgba(0,0,0,0.8)',
            color: 'white',
            padding: '20px',
            borderRadius: '10px',
            zIndex: 1500,
            fontSize: '18px',
            textAlign: 'center',
            maxWidth: '80%',
            wordWrap: 'break-word'
        });

        $('body').append(hintDiv);

        setTimeout(() => {
            hintDiv.fadeOut(500, () => hintDiv.remove());
        }, 3000);
    }

    clearHintHighlights() {
        $('.card-hint').removeClass('card-hint');
        $('.pile-hint').removeClass('pile-hint');
    }

    handleNoMovesAvailable() {
        if (this.isGameDeadlocked()) {
            this.showGameOverMessage();
        } else {
            this.showHintMessage('No obvious moves available, try drawing cards or re-examining the board');
        }
    }

    isGameDeadlocked() {
        if (this.stock.length > 0) {
            return false;
        }

        if (this.stock.length === 0 && this.waste.length > 0) {
            return false;
        }

        if (this.waste.length > 0) {
            const wasteCard = this.waste[this.waste.length - 1];
            if (wasteCard.faceUp && this.canMoveToFoundationByValue(wasteCard.suit, wasteCard.value)) {
                return false;
            }

            for (let i = 0; i < 7; i++) {
                if (this.canMoveToTableauPile(i, wasteCard)) {
                    return false;
                }
            }
        }

        for (let i = 0; i < 7; i++) {
            const pile = this.tableau[i];
            if (pile.length > 0) {
                const topCard = pile[pile.length - 1];
                if (topCard.faceUp) {
                    if (this.canMoveToFoundationByValue(topCard.suit, topCard.value)) {
                        return false;
                    }

                    for (let j = 0; j < 7; j++) {
                        if (i !== j && this.canMoveToTableauPile(j, topCard)) {
                            return false;
                        }
                    }
                }
            }
        }

        return true;
    }

    showGameOverMessage() {
        const messageDiv = $('<div>').addClass('game-over-message');
        messageDiv.html(`
            <div style="text-align: center; padding: 30px; background: rgba(0,0,0,0.9); color: white; border-radius: 15px; max-width: 400px;">
                <h3 style="margin-bottom: 20px; color: #ff6b6b;">Game Unsolvable</h3>
                <p style="margin-bottom: 25px; line-height: 1.5;">
                    No moves available in current situation, suggest restarting the game.
                </p>
                <div style="display: flex; gap: 15px; justify-content: center;">
                    <button id="restart-game-btn" style="padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        Restart
                    </button>
                    <button id="return-home-btn" style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        Home
                    </button>
                </div>
            </div>
        `);

        messageDiv.css({
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 2000
        });

        $('body').append(messageDiv);

        $('#restart-game-btn').on('click', () => {
            messageDiv.remove();
            this.newGame();
        });

        $('#return-home-btn').on('click', () => {
            messageDiv.remove();
            window.location.href = '/';
        });
    }
    findBasicMoves() {
        const moves = [];

        if (this.waste.length > 0) {
            const wasteCard = this.waste[this.waste.length - 1];
            if (wasteCard.faceUp) {
                if (this.canMoveToFoundationByValue(wasteCard.suit, wasteCard.value)) {
                    moves.push({
                        from: { type: 'waste', card: wasteCard },
                        to: { type: 'foundation', suit: wasteCard.suit }
                    });
                }

                for (let i = 0; i < 7; i++) {
                    if (this.canMoveToTableauPile(i, wasteCard)) {
                        moves.push({
                            from: { type: 'waste', card: wasteCard },
                            to: { type: 'tableau', index: i }
                        });
                    }
                }
            }
        }

        for (let i = 0; i < 7; i++) {
            const pile = this.tableau[i];
            if (pile.length === 0) continue;

            let topFaceUpIndex = -1;
            for (let j = pile.length - 1; j >= 0; j--) {
                if (pile[j].faceUp) {
                    topFaceUpIndex = j;
                    break;
                }
            }

            if (topFaceUpIndex === -1) continue;

            const topCard = pile[topFaceUpIndex];
            if (this.canMoveToFoundationByValue(topCard.suit, topCard.value)) {
                moves.push({
                    from: { type: 'tableau', index: i, card: topCard },
                    to: { type: 'foundation', suit: topCard.suit }
                });
            }

            for (let start = topFaceUpIndex; start < pile.length; start++) {
                const sequence = pile.slice(start);
                if (!this.isValidTableauSequence(sequence)) continue;

                for (let j = 0; j < 7; j++) {
                    if (i === j) continue;

                    if (this.canMoveSequenceToTableauPile(j, sequence)) {
                        moves.push({
                            from: { type: 'tableau', index: i, card: sequence[0], sequence: sequence },
                            to: { type: 'tableau', index: j }
                        });
                        break;
                    }
                }
            }
        }

        return moves;
    }





    canMoveToTableauPile(pileIndex, card) {
        const targetPile = this.tableau[pileIndex];

        if (targetPile.length === 0) {
            return card.value === 13;
        }

        const topCard = targetPile[targetPile.length - 1];
        return topCard.faceUp && topCard.color !== card.color && topCard.value === card.value + 1;
    }

    canMoveSequenceToTableauPile(pileIndex, sequence) {
        if (sequence.length === 0) return false;

        const targetPile = this.tableau[pileIndex];
        const firstCard = sequence[0];

        if (targetPile.length === 0) {
            return firstCard.value === 13;
        }

        const topCard = targetPile[targetPile.length - 1];
        return topCard.faceUp && topCard.color !== firstCard.color && topCard.value === firstCard.value + 1;
    }



    recordMove(moveData) {
        this.moveHistory.push(moveData);
        if (this.moveHistory.length > 50) {
            this.moveHistory.shift();
        }
    }

    async autoComplete() {
        if (this.gameWon || this.isAutoCompleting) return;
        this.isAutoCompleting = true;

        let allFaceUp = true;
        for (let i = 0; i < 7; i++) {
            for (let j = 0; j < this.tableau[i].length; j++) {
                if (!this.tableau[i][j].faceUp) {
                    allFaceUp = false;
                    break;
                }
            }
            if (!allFaceUp) break;
        }
        if (!allFaceUp) {
            this.isAutoCompleting = false;
            return;
        }

        this.fastAnimationMode = true;
        const startTime = Date.now();
        let completedMoves = 0;

        const performParallelAutoComplete = () => {
            let foundAnyMove = false;
            const currentBatch = [];

            if (this.waste.length > 0) {
                const wasteCard = this.waste[this.waste.length - 1];
                if (this.canMoveToFoundationByValue(wasteCard.suit, wasteCard.value)) {
                    const element = $('.waste-pile .card').last();
                    if (element.length > 0) {
                        currentBatch.push({
                            card: wasteCard,
                            from: { type: 'waste' },
                            element: element
                        });
                        foundAnyMove = true;
                    }
                }
            }

            for (let i = 0; i < 7; i++) {
                const pile = this.tableau[i];
                if (pile.length > 0) {
                    const topCard = pile[pile.length - 1];
                    if (topCard.faceUp && this.canMoveToFoundationByValue(topCard.suit, topCard.value)) {
                        const element = $(`#tableau-${i} .card`).last();
                        if (element.length > 0) {
                            currentBatch.push({
                                card: topCard,
                                from: { type: 'tableau', index: i },
                                element: element
                            });
                            foundAnyMove = true;
                        }
                    }
                }
            }

            if (foundAnyMove) {
                currentBatch.forEach((moveData, index) => {
                    setTimeout(() => {
                        this.performParallelAutoMove(moveData, () => {
                            completedMoves++;
                            if (completedMoves >= currentBatch.length) {
                                completedMoves = 0;
                                setTimeout(() => {
                                    if (this.isAutoCompleting) {
                                        performParallelAutoComplete();
                                    }
                                }, 100);
                            }
                        });
                    }, index * 50);
                });
            } else {
                this.fastAnimationMode = false;
                this.isAutoCompleting = false;
                this.checkWinCondition();

                const totalTime = Date.now() - startTime;
                console.log(`Auto-collect completed, time taken: ${totalTime}ms`);
            }
        };

        performParallelAutoComplete();
    }

    performParallelAutoMove(moveData, callback) {
        const { card, from, element } = moveData;

        if (this.testMode) {
            this.executeAutoCompleteMove(card, from);
            if (callback) callback();
        } else {
            const targetEl = $(`#foundation-${card.suit}`);
            this.animateCardFlyFast(element, targetEl, () => {
                this.executeAutoCompleteMove(card, from);
                if (callback) callback();
            });
        }
    }

    executeAutoCompleteMove(card, from) {
        let moveData = {
            type: 'move',
            to: { type: 'foundation', suit: card.suit, pile: this.foundations[card.suit] },
            card: card
        };

        if (from.type === 'waste' && this.waste.length > 0) {
            const c = this.waste.pop();
            if (this.canMoveToFoundationByValue(card.suit, c.value)) {
                c.faceUp = true;
                this.foundations[card.suit].push(c);
                this.score += 10;
                moveData.from = { type: 'waste', pile: this.waste };
            } else {
                this.waste.push(c);
                return false;
            }
        } else if (from.type === 'tableau') {
            const pile = this.tableau[from.index];
            if (pile.length > 0) {
                const c = pile.pop();
                if (this.canMoveToFoundationByValue(card.suit, c.value)) {
                    c.faceUp = true;
                    this.foundations[card.suit].push(c);
                    this.score += 10;
                    moveData.from = { type: 'tableau', index: from.index, pile: pile };
                    if (pile.length > 0 && !pile[pile.length - 1].faceUp) {
                        pile[pile.length - 1].faceUp = true;
                        this.score += 5;
                        moveData.flippedCard = pile[pile.length - 1];
                    }
                } else {
                    pile.push(c);
                    return false;
                }
            }
        }

        this.recordMove(moveData);
        this.moves++;
        this.updateDisplayWithoutAutoComplete();
        return true;
    }

    async checkAutoCompleteTrigger() {
        if (this.gameWon || !this.autoCompleteEnabled || this.isAutoCompleting || this.isAnimatingCard) return;

        let allFaceUp = true;

        for (let i = 0; i < 7; i++) {
            for (let j = 0; j < this.tableau[i].length; j++) {
                if (!this.tableau[i][j].faceUp) {
                    allFaceUp = false;
                    break;
                }
            }
            if (!allFaceUp) break;
        }

        if (allFaceUp) {
            for (let i = 0; i < this.waste.length; i++) {
                if (!this.waste[i].faceUp) {
                    allFaceUp = false;
                    break;
                }
            }
        }

        if (allFaceUp && this.stock.length > 0) {
            allFaceUp = false;
        }

        if (allFaceUp) {
            await this.autoComplete();
        }
    }

    onKeyDown(e) {
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'SELECT') return;

        switch(e.key.toLowerCase()) {
            case 'n':
                if (e.ctrlKey) {
                    e.preventDefault();
                    this.newGame();
                }
                break;
            case 'z':
                if (e.ctrlKey) {
                    e.preventDefault();
                    this.undoMove();
                }
                break;
            case 'h':
                e.preventDefault();
                this.showKlondikeHint();
                break;
            case ' ':
            case 'enter':
                e.preventDefault();
                this.drawFromStock();
                break;
            case 'escape':
                e.preventDefault();
                this.hideMessage();
                this.hideHelp();
                break;
            case 'a':
                if (e.ctrlKey) {
                    e.preventDefault();
                    this.autoComplete();
                }
                break;
            case 'f':
                if (e.ctrlKey) {
                    e.preventDefault();
                    this.toggleFullscreen();
                }
                break;
        }
    }

    toggleFullscreen() {
        if (!document.fullscreenElement &&
            !document.webkitFullscreenElement &&
            !document.mozFullScreenElement &&
            !document.msFullscreenElement) {
            // Enter fullscreen
            const element = document.documentElement;
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullscreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            }
        } else {
            // Exit fullscreen
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            }
        }
    }

    updateFullscreenButton() {
        const isFullscreen = document.fullscreenElement ||
                           document.webkitFullscreenElement ||
                           document.mozFullScreenElement ||
                           document.msFullscreenElement;

        const button = $('#fullscreenBtn');
        if (isFullscreen) {
            button.text('⛶').attr('title', 'Exit Fullscreen (Ctrl+F)');
        } else {
            button.text('⛶').attr('title', 'Enter Fullscreen (Ctrl+F)');
        }
    }

    autoFullscreen() {
        if (!document.fullscreenElement &&
            !document.webkitFullscreenElement &&
            !document.mozFullScreenElement &&
            !document.msFullscreenElement) {

            const element = document.documentElement;
            if (element.requestFullscreen) {
                element.requestFullscreen().catch(() => {});
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullscreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            }
        }
    }
}

$(document).ready(() => {
    new SolitaireGame();
});
