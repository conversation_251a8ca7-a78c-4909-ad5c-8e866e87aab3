<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蜘蛛纸牌 - Spider Solitaire</title>
    <meta name="description" content="经典蜘蛛纸牌游戏，支持三种难度等级，流畅的动画效果和直观的操作体验。">
    <meta name="keywords" content="蜘蛛纸牌, spider solitaire, 纸牌游戏, 单人纸牌, 在线游戏">
    
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <meta name="theme-color" content="#2d5016">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/spider.css">
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div id="landscape-prompt" class="landscape-prompt">
        <div class="landscape-content">
            <div class="rotate-icon">📱</div>
            <h2>横屏体验更佳</h2>
            <p>请将设备旋转至横屏模式以获得最佳蜘蛛纸牌游戏体验</p>
        </div>
    </div>

    <div class="game-container">
        <!-- Header -->
        <header class="game-header">
            <div class="header-left">
                <h1>蜘蛛纸牌</h1>
                <div class="game-stats">
                    <div class="stat">
                        <span class="stat-label">得分:</span>
                        <span id="score">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">时间:</span>
                        <span id="timer">00:00</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">移动:</span>
                        <span id="moves">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">完成:</span>
                        <span id="completed">0/8</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="difficulty-buttons">
                    <button id="easyBtn" class="btn btn-difficulty active">简单</button>
                    <button id="mediumBtn" class="btn btn-difficulty">中等</button>
                    <button id="hardBtn" class="btn btn-difficulty">困难</button>
                </div>
                <button id="newGameBtn" class="btn btn-primary">新游戏</button>
                <button id="undoBtn" class="btn btn-secondary">撤销</button>
                <button id="hintBtn" class="btn btn-secondary">提示</button>
                <button id="helpBtn" class="btn btn-secondary">帮助</button>
                <button id="fullscreenBtn" class="btn btn-secondary">⛶</button>
            </div>
        </header>

        <!-- Game Board -->
        <main class="game-board spider-board">
            <!-- Stock Pile -->
            <div class="stock-area">
                <div class="stock-pile" id="stock">
                    <div class="card-back"></div>
                </div>
                <div class="stock-info">
                    <p>点击发牌</p>
                    <small>每次发10张牌</small>
                </div>
            </div>

            <!-- Tableau Piles (10 columns for Spider) -->
            <div class="tableau-area spider-tableau">
                <div class="tableau-pile" id="tableau-0"></div>
                <div class="tableau-pile" id="tableau-1"></div>
                <div class="tableau-pile" id="tableau-2"></div>
                <div class="tableau-pile" id="tableau-3"></div>
                <div class="tableau-pile" id="tableau-4"></div>
                <div class="tableau-pile" id="tableau-5"></div>
                <div class="tableau-pile" id="tableau-6"></div>
                <div class="tableau-pile" id="tableau-7"></div>
                <div class="tableau-pile" id="tableau-8"></div>
                <div class="tableau-pile" id="tableau-9"></div>
            </div>

            <!-- Completed Sequences Area -->
            <div class="completed-area">
                <div class="completed-sequences" id="completedSequences">
                    <div class="completed-placeholder">完成的牌组将显示在这里</div>
                </div>
            </div>
        </main>

        <!-- Game Messages -->
        <div id="gameMessage" class="game-message hidden">
            <div class="message-content">
                <h2 id="messageTitle">恭喜！</h2>
                <p id="messageText">您赢得了游戏！</p>
                <div class="message-stats">
                    <div>得分: <span id="finalScore">0</span></div>
                    <div>时间: <span id="finalTime">00:00</span></div>
                    <div>移动: <span id="finalMoves">0</span></div>
                </div>
                <div class="message-buttons">
                    <button id="playAgainBtn" class="btn btn-primary">再玩一局</button>
                    <button id="closeMessageBtn" class="btn btn-secondary">关闭</button>
                </div>
            </div>
        </div>

        <!-- Help Panel -->
        <div id="helpPanel" class="help-panel hidden">
            <div class="help-content">
                <div class="help-header">
                    <h3>🕷️ 蜘蛛纸牌</h3>
                    <button id="closeHelpBtn" class="close-btn">×</button>
                </div>

                <div class="help-body">
                    <div class="help-section">
                        <h4>🎯 游戏目标</h4>
                        <p>将所有104张牌按花色从K到A的顺序排列，完成8个完整牌组。</p>
                    </div>

                    <div class="help-section">
                        <h4>📋 游戏规则</h4>
                        <ul>
                            <li><strong>移动规则:</strong> 牌可以按递减顺序放置（不限花色）</li>
                            <li><strong>多牌移动:</strong> 只有同花色且连续递减的牌组才能一起移动</li>
                            <li><strong>完成牌组:</strong> 同花色K到A的完整序列会自动移除</li>
                            <li><strong>发牌:</strong> 点击牌库向每列发一张牌（需要每列都有牌）</li>
                            <li><strong>翻牌:</strong> 移走牌后，下面的背面牌会自动翻开</li>
                        </ul>
                    </div>

                    <div class="help-section">
                        <h4>🎮 难度等级</h4>
                        <ul>
                            <li><strong>简单:</strong> 只使用黑桃一种花色</li>
                            <li><strong>中等:</strong> 使用黑桃和红心两种花色</li>
                            <li><strong>困难:</strong> 使用全部四种花色</li>
                        </ul>
                    </div>

                    <div class="help-section">
                        <h4>⚡ 操作技巧</h4>
                        <ul>
                            <li><strong>拖拽:</strong> 拖动牌或牌组到目标位置</li>
                            <li><strong>双击:</strong> 双击牌尝试自动移动到合适位置</li>
                            <li><strong>多选:</strong> 点击牌组中的任意牌，会选择该牌及其下方的所有连续同花色牌</li>
                        </ul>
                    </div>

                    <div class="help-section">
                        <h4>🏆 计分系统</h4>
                        <ul>
                            <li>翻开背面牌: <strong>+5分</strong></li>
                            <li>完成一个牌组: <strong>+100分</strong></li>
                            <li>移动牌组: <strong>+1分</strong></li>
                        </ul>
                    </div>
                </div>

                <div class="help-footer">
                    <button id="closeHelpBtnBottom" class="btn btn-primary">开始游戏！</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/spider.js"></script>
</body>
</html>

