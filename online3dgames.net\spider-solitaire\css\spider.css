/* Spider Solitaire specific styles */

/* Game board layout for Spider */
.spider-board {
    display: grid;
    grid-template-areas: 
        "tableau tableau"
        "completed stock";
    grid-template-columns: 1fr auto;
    gap: 20px;
    margin-bottom: 30px;
    position: relative;
    min-height: 100vh;
}

/* Stock area for <PERSON> */
.spider-board .stock-area {
    grid-area: stock;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 20px;
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    border: 2px solid rgba(255,255,255,0.1);
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 100;
}

.stock-info {
    text-align: center;
    color: rgba(255,255,255,0.8);
}

.stock-info p {
    margin: 0;
    font-size: 14px;
    font-weight: bold;
}

.stock-info small {
    font-size: 12px;
    opacity: 0.7;
}

/* Completed sequences area */
.completed-area {
    grid-area: completed;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 20px;
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 100;
}

.completed-sequences {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    max-width: 400px;
    justify-content: flex-end;
}

.completed-placeholder {
    color: rgba(255,255,255,0.5);
    font-style: italic;
    text-align: center;
    padding: 20px;
    border: 2px dashed rgba(255,255,255,0.2);
    border-radius: 10px;
    background: rgba(255,255,255,0.05);
}

.completed-sequence {
    width: 80px;
    height: 110px;
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    border-radius: 8px;
    border: 2px solid #4CAF50;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 24px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    animation: completedSequenceAppear 0.5s ease-out;
}

@keyframes completedSequenceAppear {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Spider tableau with 10 columns */
.spider-tableau {
    grid-area: tableau;
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    gap: 8px;
    justify-content: center;
    margin-top: 20px;
    padding-bottom: 120px;
}

.spider-tableau .tableau-pile {
    width: auto;
    min-width: 80px;
    height: 120px;
    border: 2px dashed rgba(255,255,255,0.2);
    border-radius: 8px;
    background: rgba(255,255,255,0.05);
    position: relative;
}

/* Difficulty buttons */
.difficulty-buttons {
    display: flex;
    gap: 5px;
    margin-right: 10px;
}

.btn-difficulty {
    padding: 8px 16px;
    font-size: 12px;
    border-radius: 20px;
    background: rgba(255,255,255,0.1);
    color: rgba(255,255,255,0.8);
    border: 1px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
}

.btn-difficulty:hover {
    background: rgba(255,255,255,0.2);
    color: white;
    border-color: rgba(255,255,255,0.4);
}

.btn-difficulty.active {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    border-color: #ff6b6b;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Multi-card selection styles */
.card.selected {
    box-shadow: 0 0 0 2px #ffeb3b, 0 4px 8px rgba(0,0,0,0.3);
    transform: translateY(-2px);
}

.card.multi-selected {
    box-shadow: 0 0 0 2px #4CAF50, 0 4px 8px rgba(0,0,0,0.3);
    transform: translateY(-1px);
}

/* Card sequence highlighting */
.card.sequence-valid {
    box-shadow: 0 0 0 2px #4CAF50, 0 4px 8px rgba(76, 175, 80, 0.4);
}

.card.sequence-invalid {
    box-shadow: 0 0 0 2px #f44336, 0 4px 8px rgba(244, 67, 54, 0.4);
}

/* Enhanced drag feedback for multiple cards */
.card.dragging-multi {
    z-index: 9999 !important;
    pointer-events: none;
    position: fixed !important;
    box-shadow: 0 15px 30px rgba(0,0,0,0.7), 0 0 0 3px #ffeb3b;
    transform: scale(1.08) rotate(2deg);
    transition: none !important;
    filter: brightness(1.1);
}

.card.dragging-multi + .card.dragging-multi {
    z-index: 9998 !important;
    box-shadow: 0 12px 24px rgba(0,0,0,0.6), 0 0 0 2px #4CAF50;
    transform: scale(1.05) rotate(1deg);
    filter: brightness(1.05);
}

.card.dragging-multi + .card.dragging-multi + .card.dragging-multi {
    z-index: 9997 !important;
    box-shadow: 0 10px 20px rgba(0,0,0,0.5), 0 0 0 2px #2196F3;
    transform: scale(1.03);
    filter: brightness(1.02);
}

/* Smooth drag transition */
.card.drag-preparing {
    transition: all 0.15s ease-out;
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 6px 12px rgba(0,0,0,0.3);
}

/* Drop zone feedback improvements */
.drop-zone-valid {
    background: rgba(76, 175, 80, 0.25) !important;
    border: 3px solid rgba(76, 175, 80, 0.8) !important;
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.6) !important;
    transform: scale(1.02);
    transition: all 0.2s ease;
}

.drop-zone-invalid {
    background: rgba(244, 67, 54, 0.15) !important;
    border: 3px solid rgba(244, 67, 54, 0.6) !important;
    box-shadow: 0 0 10px rgba(244, 67, 54, 0.4) !important;
    animation: invalidShake 0.3s ease-in-out;
}

@keyframes invalidShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

/* Stock pile enhancements for Spider */
.spider-board .stock-pile {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.spider-board .stock-pile:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.4);
}

.spider-board .stock-pile:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Empty stock styling */
.spider-board .stock-pile.empty {
    background: rgba(255,255,255,0.05);
    border: 2px dashed rgba(255,255,255,0.3);
    cursor: not-allowed;
}

.spider-board .stock-pile.empty:hover {
    transform: none;
    box-shadow: none;
}

/* Responsive design for Spider */
@media (max-width: 1200px) {
    .spider-tableau {
        grid-template-columns: repeat(10, minmax(60px, 1fr));
        gap: 4px;
    }
    
    .spider-tableau .tableau-pile {
        min-width: 60px;
        height: 90px;
    }
    
    .completed-sequences {
        max-width: 300px;
    }
    
    .completed-sequence {
        width: 60px;
        height: 80px;
        font-size: 18px;
    }
}

@media (max-width: 1024px) {
    .spider-board {
        grid-template-areas: 
            "tableau"
            "completed"
            "stock";
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .spider-board .stock-area {
        position: fixed;
        bottom: 10px;
        right: 10px;
        padding: 15px;
    }
    
    .completed-area {
        position: fixed;
        bottom: 10px;
        left: 10px;
        padding: 15px;
    }
    
    .spider-board .stock-area {
        flex-direction: row;
        justify-content: center;
        padding: 15px;
        position: fixed;
        bottom: 10px;
        right: 10px;
    }
    
    .completed-area {
        justify-content: center;
        padding: 10px;
        position: fixed;
        bottom: 10px;
        left: 10px;
    }
    
    .completed-sequences {
        max-width: 100%;
        justify-content: center;
    }
    
    .spider-tableau {
        grid-template-columns: repeat(5, 1fr);
        gap: 6px;
    }
    
    .spider-tableau .tableau-pile {
        min-width: 50px;
        height: 70px;
    }
    
    .difficulty-buttons {
        flex-wrap: wrap;
        gap: 3px;
    }
    
    .btn-difficulty {
        padding: 6px 12px;
        font-size: 11px;
    }
}

@media (max-width: 768px) {
    .spider-tableau {
        grid-template-columns: repeat(5, 1fr);
        gap: 3px;
        padding-bottom: 100px;
    }
    
    .spider-tableau .tableau-pile {
        min-width: 45px;
        height: 60px;
    }
    
    .spider-board .stock-area {
        bottom: 5px;
        right: 5px;
        padding: 10px;
    }
    
    .completed-area {
        bottom: 5px;
        left: 5px;
        padding: 10px;
    }
    
    .spider-board .stock-area {
        padding: 10px;
    }
    
    .stock-info p {
        font-size: 12px;
    }
    
    .stock-info small {
        font-size: 10px;
    }
    
    .completed-sequence {
        width: 50px;
        height: 65px;
        font-size: 16px;
    }
    
    .game-stats {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .stat {
        padding: 6px 12px;
        font-size: 12px;
    }
}

/* Enhanced card animations for Spider Solitaire */

/* Base card transition for smooth animations */
.card {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Flying card animations - optimized for performance */
.card.flying {
    z-index: 10000 !important;
    pointer-events: none;
    position: fixed !important;
    transition: all 0.4s linear !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.4);
    will-change: transform, left, top;
}

.card.flying-to-tableau {
    z-index: 10000 !important;
    pointer-events: none;
    position: fixed !important;
    transition: all 0.4s linear !important;
    box-shadow: 0 12px 30px rgba(0,0,0,0.6);
    will-change: transform, left, top;
}

.card.flying-to-completed {
    z-index: 9999 !important;
    pointer-events: none;
    position: fixed !important;
    transition: all 0.6s linear !important;
    box-shadow: 0 6px 20px rgba(0,0,0,0.5);
    transform: scale(0.9) !important;
    will-change: transform, left, top;
}

.card.flying-from-stock {
    z-index: 10001 !important;
    pointer-events: none;
    position: fixed !important;
    transition: all 0.4s linear !important;
    box-shadow: 0 10px 25px rgba(0,0,0,0.5);
    transform: scale(1.05) !important;
    will-change: transform, left, top;
}

/* Deal animation for cards from stock */
.card.dealing {
    animation: dealCard 0.3s ease-out;
}

@keyframes dealCard {
    0% {
        opacity: 0;
        transform: translateY(-50px) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Success animation for completed sequences */
.card.completing-sequence {
    animation: completeSequence 0.8s ease-out forwards;
}

@keyframes completeSequence {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 20px rgba(76, 175, 80, 0.8);
    }
    100% {
        transform: scale(0.8);
        opacity: 0;
    }
}

/* Card return animation when move is invalid */
.card.returning {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    z-index: 9998 !important;
}

/* Enhanced drag animations */
.card.drag-preparing {
    transition: all 0.15s ease-out;
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 6px 12px rgba(0,0,0,0.3);
}

.card.dragging {
    z-index: 9999 !important;
    pointer-events: none;
    position: fixed !important;
    box-shadow: 0 8px 20px rgba(0,0,0,0.5);
    transform: scale(1.05) rotate(1deg);
    transition: none !important;
}

.card.dragging-multi {
    z-index: 9999 !important;
    pointer-events: none;
    position: fixed !important;
    box-shadow: 0 15px 30px rgba(0,0,0,0.7), 0 0 0 3px #ffeb3b;
    transform: scale(1.08) rotate(2deg);
    transition: none !important;
    filter: brightness(1.1);
}

.card.dragging-multi + .card.dragging-multi {
    z-index: 9998 !important;
    box-shadow: 0 12px 24px rgba(0,0,0,0.6), 0 0 0 2px #4CAF50;
    transform: scale(1.05) rotate(1deg);
    filter: brightness(1.05);
}

.card.dragging-multi + .card.dragging-multi + .card.dragging-multi {
    z-index: 9997 !important;
    box-shadow: 0 10px 20px rgba(0,0,0,0.5), 0 0 0 2px #2196F3;
    transform: scale(1.03);
    filter: brightness(1.02);
}

/* Drop zone feedback */
.drop-zone-valid {
    background: rgba(76, 175, 80, 0.25) !important;
    border: 3px solid rgba(76, 175, 80, 0.8) !important;
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.6) !important;
    transform: scale(1.02);
    transition: all 0.2s ease;
}

.drop-zone-invalid {
    background: rgba(244, 67, 54, 0.15) !important;
    border: 3px solid rgba(244, 67, 54, 0.6) !important;
    box-shadow: 0 0 10px rgba(244, 67, 54, 0.4) !important;
    animation: invalidShake 0.3s ease-in-out;
}

@keyframes invalidShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

/* Stock pile animations */
.stock-pile:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.4);
    transition: all 0.3s ease;
}

.stock-pile:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Completed sequence animations */
.completed-sequence {
    animation: completedSequenceAppear 0.5s ease-out;
}

@keyframes completedSequenceAppear {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Sequence highlighting */
.card.sequence-valid {
    box-shadow: 0 0 0 2px #4CAF50, 0 4px 8px rgba(76, 175, 80, 0.4);
}

.card.sequence-invalid {
    box-shadow: 0 0 0 2px #f44336, 0 4px 8px rgba(244, 67, 54, 0.4);
}

/* Smooth transitions for all card states */
.card {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

/* Animation for card placement */
.card.placing {
    animation: placeCard 0.3s ease-out;
}

@keyframes placeCard {
    0% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Animation for card removal */
.card.removing {
    animation: removeCard 0.3s ease-in;
}

@keyframes removeCard {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(0.8);
        opacity: 0;
    }
}

/* Enhanced tableau pile animations */
.tableau-pile {
    transition: all 0.2s ease;
}

.tableau-pile:hover {
    background: rgba(255,255,255,0.08);
    border-color: rgba(255,255,255,0.3);
}

/* Animation for auto-complete sequences */
.auto-completing {
    animation: autoComplete 1s ease-out;
}

@keyframes autoComplete {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    25% {
        transform: scale(1.05);
        box-shadow: 0 0 15px rgba(76, 175, 80, 0.6);
    }
    75% {
        transform: scale(1.1);
        box-shadow: 0 0 25px rgba(76, 175, 80, 0.8);
    }
    100% {
        transform: scale(0.9);
        opacity: 0;
    }
}

/* Animation for stock dealing */
.stock-dealing {
    animation: stockDeal 0.4s ease-out;
}

@keyframes stockDeal {
    0% {
        transform: translateY(-20px) scale(0.9);
        opacity: 0;
    }
    50% {
        transform: translateY(-10px) scale(1.05);
        opacity: 0.8;
    }
    100% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

/* Animation for score updates */
.score-updating {
    animation: scoreUpdate 0.5s ease-out;
}

@keyframes scoreUpdate {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
        color: #4CAF50;
    }
    100% {
        transform: scale(1);
    }
}

/* Animation for move counter updates */
.moves-updating {
    animation: movesUpdate 0.3s ease-out;
}

@keyframes movesUpdate {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
        color: #2196F3;
    }
    100% {
        transform: scale(1);
    }
}

/* Animation for completed counter updates */
.completed-updating {
    animation: completedUpdate 0.6s ease-out;
}

@keyframes completedUpdate {
    0% {
        transform: scale(1);
    }
    25% {
        transform: scale(1.3);
        color: #FF9800;
    }
    75% {
        transform: scale(1.1);
        color: #FFC107;
    }
    100% {
        transform: scale(1);
    }
}

/* Hint highlighting */
.card.hint-highlight {
    animation: hintPulse 1s ease-in-out infinite;
}

@keyframes hintPulse {
    0%, 100% {
        box-shadow: 0 0 0 2px transparent;
    }
    50% {
        box-shadow: 0 0 0 2px #ffeb3b, 0 0 10px rgba(255, 235, 59, 0.5);
    }
}

/* Responsive animations for mobile */
@media (max-width: 768px) {
    .card.flying,
    .card.flying-to-tableau,
    .card.flying-to-completed,
    .card.flying-from-stock {
        transition: all 0.4s linear !important;
    }
    
    .card.dragging {
        transform: scale(1.03) rotate(0.5deg);
    }
    
    .card.dragging-multi {
        transform: scale(1.05) rotate(1deg);
    }
}

/* Animation performance optimizations */
.card {
    will-change: transform, opacity, box-shadow;
    backface-visibility: hidden;
    transform: translateZ(0);
}

/* Disable animations for users who prefer reduced motion */
@media (prefers-reduced-motion: reduce) {
    .card,
    .card.flying,
    .card.flying-to-tableau,
    .card.flying-to-completed,
    .card.flying-from-stock,
    .card.dragging,
    .card.dragging-multi {
        transition: none !important;
        animation: none !important;
    }
}

/* Disabled card styles for Spider Solitaire */
.card.disabled-card {
    filter: grayscale(100%);
    cursor: not-allowed;
    pointer-events: none;
}

.card.disabled-card:hover {
    transform: none;
    box-shadow: none;
}

.card.disabled-card:active {
    transform: none;
    box-shadow: none;
}

